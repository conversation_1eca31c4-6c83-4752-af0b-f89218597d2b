package main

import (
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/learning"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("✅ 批准学习到的知识...")

	// 创建知识学习器
	knowledgeLearner := learning.NewKnowledgeLearner(db, nil, nil)

	// 获取待审核的知识
	pendingKnowledge, err := knowledgeLearner.GetPendingKnowledge(10)
	if err != nil {
		log.Fatalf("获取待审核知识失败: %v", err)
	}

	fmt.Printf("发现 %d 条待审核知识\n", len(pendingKnowledge))

	// 批准所有高置信度的知识
	for i, knowledge := range pendingKnowledge {
		fmt.Printf("\n%d. 问题: %s\n", i+1, knowledge.Question)
		fmt.Printf("   答案: %s\n", knowledge.Answer)
		fmt.Printf("   置信度: %.2f\n", knowledge.Confidence)
		
		if knowledge.Confidence >= 0.7 {
			err := knowledgeLearner.ApproveKnowledge(knowledge.ID)
			if err != nil {
				fmt.Printf("   ❌ 批准失败: %v\n", err)
			} else {
				fmt.Printf("   ✅ 已批准\n")
			}
		} else {
			fmt.Printf("   ⚠️  置信度过低，跳过\n")
		}
	}

	// 测试搜索已批准的知识
	fmt.Println("\n🔍 测试搜索已批准的知识:")
	
	queries := []string{"C#", "Python", "Java", "Go语言"}
	for _, query := range queries {
		results, err := knowledgeLearner.SearchLearnedKnowledge(query, 3)
		if err != nil {
			fmt.Printf("搜索 '%s' 失败: %v\n", query, err)
			continue
		}
		
		fmt.Printf("\n搜索 '%s' 找到 %d 条结果:\n", query, len(results))
		for j, result := range results {
			fmt.Printf("  %d. %s -> %s (置信度: %.2f, 状态: %s)\n", 
				j+1, result.Question, result.Answer, result.Confidence, result.Status)
		}
	}

	fmt.Println("\n🎉 知识批准完成！")
}
