@echo off
echo 🌐 测试所有页面访问...
echo.

echo 📋 1. 测试主页...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081/' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 主页访问成功 - 状态码:' $response.StatusCode } catch { Write-Host '❌ 主页访问失败:' $_.Exception.Message }"
echo.

echo 📊 2. 测试健康检查...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081/health' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 健康检查成功 - 状态码:' $response.StatusCode } catch { Write-Host '❌ 健康检查失败:' $_.Exception.Message }"
echo.

echo 🧠 3. 测试学习仪表板...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081/learning_dashboard.html' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 学习仪表板访问成功 - 状态码:' $response.StatusCode } catch { Write-Host '❌ 学习仪表板访问失败:' $_.Exception.Message }"
echo.

echo 🕷️ 4. 测试爬虫仪表板...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081/crawler_dashboard.html' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 爬虫仪表板访问成功 - 状态码:' $response.StatusCode } catch { Write-Host '❌ 爬虫仪表板访问失败:' $_.Exception.Message }"
echo.

echo 🤖 5. 测试问答API...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081/ask' -Method POST -ContentType 'application/json' -Body '{\"question\":\"什么是Go语言？\"}' -UseBasicParsing -TimeoutSec 10; Write-Host '✅ 问答API成功 - 状态码:' $response.StatusCode } catch { Write-Host '❌ 问答API失败:' $_.Exception.Message }"
echo.

echo 📚 6. 测试知识API...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081/api/knowledge/list' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 知识API成功 - 状态码:' $response.StatusCode } catch { Write-Host '❌ 知识API失败:' $_.Exception.Message }"
echo.

echo 📈 7. 测试统计API...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8081/api/v1/stats' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 统计API成功 - 状态码:' $response.StatusCode } catch { Write-Host '❌ 统计API失败:' $_.Exception.Message }"
echo.

echo 🎉 测试完成！
echo.
echo 📋 访问地址总结：
echo - 主页: http://localhost:8081/
echo - 学习仪表板: http://localhost:8081/learning_dashboard.html
echo - 爬虫仪表板: http://localhost:8081/crawler_dashboard.html
echo - 健康检查: http://localhost:8081/health
echo.
pause
