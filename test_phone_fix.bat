@echo off
echo 🧪 测试手机问题修复...
echo.

echo 📱 1. 测试手机相关问题...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"手机是什么\"}"
echo.
echo.

echo 📱 2. 测试另一种手机问题...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是手机\"}"
echo.
echo.

echo 🚗 3. 测试汽车问题...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"汽车是什么\"}"
echo.
echo.

echo 💻 4. 测试技术问题（对比）...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是Python\"}"
echo.
echo.

echo 🤖 5. 测试LocalAI问题（对比）...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是LocalAI\"}"
echo.
echo.

echo 🎉 测试完成！
pause
