-- 初始化学习系统数据库表
USE faqdb;

-- 1. 用户查询表
CREATE TABLE IF NOT EXISTS user_queries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    query_text TEXT NOT NULL,
    query_intent VARCHAR(100),
    query_embedding <PERSON><PERSON><PERSON>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_intent (query_intent)
);

-- 2. 系统响应表
CREATE TABLE IF NOT EXISTS system_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    query_id INT NOT NULL,
    response_text TEXT NOT NULL,
    response_intent VARCHAR(100),
    confidence_score FLOAT DEFAULT 0.0,
    processing_time_ms FLOAT DEFAULT 0.0,
    matched_faq_id INT,
    response_source VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
    INDEX idx_query_id (query_id),
    INDEX idx_confidence (confidence_score),
    INDEX idx_created_at (created_at)
);

-- 3. 用户反馈表
CREATE TABLE IF NOT EXISTS user_feedback (
    id INT AUTO_INCREMENT PRIMARY KEY,
    query_id INT NOT NULL,
    response_id INT NOT NULL,
    feedback_type ENUM('helpful', 'not_helpful', 'partially_helpful') NOT NULL,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    feedback_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
    FOREIGN KEY (response_id) REFERENCES system_responses(id) ON DELETE CASCADE,
    INDEX idx_query_id (query_id),
    INDEX idx_response_id (response_id),
    INDEX idx_feedback_type (feedback_type),
    INDEX idx_created_at (created_at)
);

-- 4. 用户行为表
CREATE TABLE IF NOT EXISTS user_behaviors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    query_id INT NOT NULL,
    behavior_type VARCHAR(100) NOT NULL,
    behavior_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
    INDEX idx_query_id (query_id),
    INDEX idx_behavior_type (behavior_type),
    INDEX idx_created_at (created_at)
);

-- 5. FAQ性能表
CREATE TABLE IF NOT EXISTS faq_performance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    faq_id INT NOT NULL,
    match_count INT DEFAULT 0,
    positive_feedback INT DEFAULT 0,
    negative_feedback INT DEFAULT 0,
    avg_rating FLOAT DEFAULT 0.0,
    avg_confidence FLOAT DEFAULT 0.0,
    last_matched TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (faq_id) REFERENCES faq(id) ON DELETE CASCADE,
    UNIQUE KEY unique_faq (faq_id),
    INDEX idx_match_count (match_count),
    INDEX idx_avg_rating (avg_rating)
);

-- 6. 学习模式表
CREATE TABLE IF NOT EXISTS learning_patterns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pattern_name VARCHAR(255) NOT NULL,
    pattern_type VARCHAR(100) NOT NULL,
    pattern_data JSON NOT NULL,
    confidence FLOAT DEFAULT 0.0,
    usage_count INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_pattern_type (pattern_type),
    INDEX idx_confidence (confidence),
    INDEX idx_last_updated (last_updated)
);

-- 7. 学习配置表
CREATE TABLE IF NOT EXISTS learning_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(255) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
);

-- 插入默认学习配置
INSERT IGNORE INTO learning_config (config_key, config_value, config_type, description) VALUES
('learning_enabled', 'true', 'boolean', '是否启用学习功能'),
('min_confidence_threshold', '0.3', 'number', '最小置信度阈值'),
('feedback_weight', '0.7', 'number', '用户反馈权重'),
('pattern_update_interval', '3600', 'number', '模式更新间隔（秒）'),
('max_learning_patterns', '1000', 'number', '最大学习模式数量'),
('auto_optimization', 'true', 'boolean', '是否启用自动优化'),
('data_retention_days', '90', 'number', '数据保留天数');

-- 为现有FAQ创建性能记录
INSERT IGNORE INTO faq_performance (faq_id, match_count, positive_feedback, negative_feedback, avg_rating, avg_confidence)
SELECT id, 0, 0, 0, 0.0, 0.0 FROM faq;

COMMIT;

-- 显示创建的表
SHOW TABLES LIKE '%user_%' OR LIKE '%learning_%' OR LIKE '%faq_performance%';

SELECT 'Learning tables initialized successfully!' as status;
