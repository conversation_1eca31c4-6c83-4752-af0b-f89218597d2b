package learning

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strings"

	"faq-system/internal/logger"
)

// queryData 查询数据结构
type queryData struct {
	text       string
	faqID      int
	confidence float32
	feedback   string
	rating     *int
}

// LearningEngine 学习引擎
type LearningEngine struct {
	db        *sql.DB
	collector *DataCollector
	config    map[string]interface{}
}

// NewLearningEngine 创建学习引擎
func NewLearningEngine(db *sql.DB) *LearningEngine {
	engine := &LearningEngine{
		db:        db,
		collector: NewDataCollector(db),
		config:    make(map[string]interface{}),
	}

	engine.loadConfig()
	return engine
}

// loadConfig 加载学习配置
func (le *LearningEngine) loadConfig() {
	rows, err := le.db.Query("SELECT config_key, config_value, config_type FROM learning_config")
	if err != nil {
		logger.Warnf("Failed to load learning config: %v", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var key, value, configType string
		if err := rows.Scan(&key, &value, &configType); err != nil {
			continue
		}

		switch configType {
		case "boolean":
			le.config[key] = value == "true"
		case "int":
			var intVal int
			fmt.Sscanf(value, "%d", &intVal)
			le.config[key] = intVal
		case "float":
			var floatVal float64
			fmt.Sscanf(value, "%f", &floatVal)
			le.config[key] = floatVal
		default:
			le.config[key] = value
		}
	}

	logger.Infof("Loaded %d learning configuration items", len(le.config))
}

// GetCollector 获取数据收集器
func (le *LearningEngine) GetCollector() *DataCollector {
	return le.collector
}

// AnalyzeQueryPatterns 分析查询模式
func (le *LearningEngine) AnalyzeQueryPatterns() error {
	if !le.getBoolConfig("learning_enabled", true) {
		return nil
	}

	logger.Info("Starting query pattern analysis...")

	// 分析相似查询模式
	if err := le.analyzeSimilarQueries(); err != nil {
		logger.Errorf("Failed to analyze similar queries: %v", err)
	}

	// 分析意图映射模式
	if err := le.analyzeIntentMapping(); err != nil {
		logger.Errorf("Failed to analyze intent mapping: %v", err)
	}

	// 分析用户偏好模式
	if err := le.analyzeUserPreferences(); err != nil {
		logger.Errorf("Failed to analyze user preferences: %v", err)
	}

	logger.Info("Query pattern analysis completed")
	return nil
}

// analyzeSimilarQueries 分析相似查询
func (le *LearningEngine) analyzeSimilarQueries() error {
	// 获取最近的查询数据
	query := `
		SELECT uq.query_text, sr.matched_faq_id, sr.confidence_score,
			   uf.feedback_type, uf.rating
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		LEFT JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
		  AND sr.matched_faq_id IS NOT NULL
		ORDER BY uq.created_at DESC
		LIMIT ?
	`

	batchSize := le.getIntConfig("learning_batch_size", 100)
	rows, err := le.db.Query(query, batchSize)
	if err != nil {
		return err
	}
	defer rows.Close()

	// 收集查询数据
	var queries []queryData
	for rows.Next() {
		var q queryData
		err := rows.Scan(&q.text, &q.faqID, &q.confidence, &q.feedback, &q.rating)
		if err != nil {
			continue
		}
		queries = append(queries, q)
	}

	// 按FAQ ID分组分析
	faqGroups := make(map[int][]queryData)
	for _, q := range queries {
		faqGroups[q.faqID] = append(faqGroups[q.faqID], q)
	}

	// 为每个FAQ分析相似查询模式
	for faqID, faqQueries := range faqGroups {
		if len(faqQueries) < 3 { // 至少需要3个查询才能分析模式
			continue
		}

		similarQueries := le.findSimilarQueries(faqQueries)
		if len(similarQueries) > 0 {
			patternData := PatternData{
				SimilarQueries: similarQueries,
			}

			le.savePattern("query_similarity", fmt.Sprintf("faq_%d_similar_queries", faqID), patternData, 0.8)
		}
	}

	return nil
}

// findSimilarQueries 查找相似查询
func (le *LearningEngine) findSimilarQueries(queries []queryData) []SimilarQuery {
	var similarQueries []SimilarQuery

	// 简单的基于词汇重叠的相似度计算
	for i, q1 := range queries {
		for j, q2 := range queries {
			if i >= j {
				continue
			}

			similarity := le.calculateTextSimilarity(q1.text, q2.text)
			if similarity > float32(le.getFloatConfig("similarity_threshold", 0.8)) {
				// 检查是否已存在
				found := false
				for k := range similarQueries {
					if similarQueries[k].Query == q1.text || similarQueries[k].Query == q2.text {
						similarQueries[k].Count++
						found = true
						break
					}
				}

				if !found {
					similarQueries = append(similarQueries, SimilarQuery{
						Query:      q1.text,
						Similarity: similarity,
						FAQID:      q1.faqID,
						Count:      1,
					})
				}
			}
		}
	}

	// 按相似度排序
	sort.Slice(similarQueries, func(i, j int) bool {
		return similarQueries[i].Similarity > similarQueries[j].Similarity
	})

	// 返回前10个
	if len(similarQueries) > 10 {
		similarQueries = similarQueries[:10]
	}

	return similarQueries
}

// calculateTextSimilarity 计算文本相似度（简单的Jaccard相似度）
func (le *LearningEngine) calculateTextSimilarity(text1, text2 string) float32 {
	words1 := strings.Fields(strings.ToLower(text1))
	words2 := strings.Fields(strings.ToLower(text2))

	// 创建词汇集合
	set1 := make(map[string]bool)
	set2 := make(map[string]bool)

	for _, word := range words1 {
		set1[word] = true
	}
	for _, word := range words2 {
		set2[word] = true
	}

	// 计算交集和并集
	intersection := 0
	union := make(map[string]bool)

	for word := range set1 {
		union[word] = true
		if set2[word] {
			intersection++
		}
	}
	for word := range set2 {
		union[word] = true
	}

	if len(union) == 0 {
		return 0
	}

	return float32(intersection) / float32(len(union))
}

// analyzeIntentMapping 分析意图映射
func (le *LearningEngine) analyzeIntentMapping() error {
	query := `
		SELECT uq.query_intent, sr.matched_faq_id, sr.confidence_score,
			   COUNT(*) as count, AVG(sr.confidence_score) as avg_confidence
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
		  AND uq.query_intent IS NOT NULL
		  AND sr.matched_faq_id IS NOT NULL
		GROUP BY uq.query_intent, sr.matched_faq_id
		HAVING count >= ?
		ORDER BY avg_confidence DESC
	`

	minThreshold := le.getIntConfig("min_feedback_threshold", 5)
	rows, err := le.db.Query(query, minThreshold)
	if err != nil {
		return err
	}
	defer rows.Close()

	intentMappings := make(map[string]map[string]float32)

	for rows.Next() {
		var intent string
		var faqID string
		var confidence float32
		var count int
		var avgConfidence float32

		if err := rows.Scan(&intent, &faqID, &confidence, &count, &avgConfidence); err != nil {
			continue
		}

		if intentMappings[intent] == nil {
			intentMappings[intent] = make(map[string]float32)
		}

		// 使用加权平均（考虑频次和置信度）
		weight := float32(math.Log(float64(count))) * avgConfidence
		intentMappings[intent][faqID] = weight
	}

	// 保存意图映射模式
	for intent, mapping := range intentMappings {
		patternData := PatternData{
			IntentMapping: mapping,
		}

		confidence := le.calculateMappingConfidence(mapping)
		le.savePattern("intent_mapping", fmt.Sprintf("intent_%s_mapping", intent), patternData, confidence)
	}

	return nil
}

// calculateMappingConfidence 计算映射置信度
func (le *LearningEngine) calculateMappingConfidence(mapping map[string]float32) float32 {
	if len(mapping) == 0 {
		return 0
	}

	var total float32
	var max float32

	for _, weight := range mapping {
		total += weight
		if weight > max {
			max = weight
		}
	}

	// 置信度基于最大权重占总权重的比例
	return max / total
}

// analyzeUserPreferences 分析用户偏好
func (le *LearningEngine) analyzeUserPreferences() error {
	// 分析用户行为模式
	query := `
		SELECT ub.behavior_type, ub.behavior_data, COUNT(*) as frequency
		FROM user_behaviors ub
		WHERE ub.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
		GROUP BY ub.behavior_type, ub.behavior_data
		HAVING frequency >= 3
		ORDER BY frequency DESC
	`

	rows, err := le.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	preferences := make(map[string]interface{})

	for rows.Next() {
		var behaviorType string
		var behaviorDataJSON string
		var frequency int

		if err := rows.Scan(&behaviorType, &behaviorDataJSON, &frequency); err != nil {
			continue
		}

		var behaviorData BehaviorData
		if err := json.Unmarshal([]byte(behaviorDataJSON), &behaviorData); err != nil {
			continue
		}

		// 分析不同类型的行为偏好
		switch behaviorType {
		case "scroll":
			if behaviorData.ScrollDepth > 0 {
				preferences["avg_scroll_depth"] = behaviorData.ScrollDepth
			}
		case "click":
			if behaviorData.ClickCount > 0 {
				preferences["avg_click_count"] = behaviorData.ClickCount
			}
		}
	}

	if len(preferences) > 0 {
		patternData := PatternData{
			UserPreferences: preferences,
		}

		le.savePattern("user_preference", "global_user_preferences", patternData, 0.7)
	}

	return nil
}

// savePattern 保存学习模式
func (le *LearningEngine) savePattern(patternType, patternName string, data PatternData, confidence float32) error {
	dataJSON, err := json.Marshal(data)
	if err != nil {
		return err
	}

	query := `
		INSERT INTO learning_patterns (pattern_type, pattern_name, pattern_data, confidence)
		VALUES (?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
		pattern_data = VALUES(pattern_data),
		confidence = VALUES(confidence),
		usage_count = usage_count + 1,
		last_updated = NOW()
	`

	_, err = le.db.Exec(query, patternType, patternName, dataJSON, confidence)
	if err != nil {
		return fmt.Errorf("failed to save pattern: %v", err)
	}

	logger.Infof("Saved learning pattern: %s/%s (confidence: %.3f)", patternType, patternName, confidence)
	return nil
}

// 配置辅助方法
func (le *LearningEngine) getBoolConfig(key string, defaultValue bool) bool {
	if val, ok := le.config[key].(bool); ok {
		return val
	}
	return defaultValue
}

func (le *LearningEngine) getIntConfig(key string, defaultValue int) int {
	if val, ok := le.config[key].(int); ok {
		return val
	}
	return defaultValue
}

func (le *LearningEngine) getFloatConfig(key string, defaultValue float64) float64 {
	if val, ok := le.config[key].(float64); ok {
		return val
	}
	return defaultValue
}
