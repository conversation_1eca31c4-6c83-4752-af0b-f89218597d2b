package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	"faq-system/internal/config"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 加载配置
	cfg := config.Load()

	fmt.Println("🔍 检查向量数据库状态...")
	fmt.Printf("📁 向量数据库路径: %s\n", cfg.VectorStore.Path)

	// 检查文件是否存在
	if _, err := os.Stat(cfg.VectorStore.Path); os.IsNotExist(err) {
		fmt.Println("❌ 向量数据库文件不存在")
		fmt.Println("🔧 尝试创建向量数据库...")
		
		// 创建数据库文件
		db, err := sql.Open("sqlite3", cfg.VectorStore.Path)
		if err != nil {
			log.Fatalf("Failed to create database: %v", err)
		}
		defer db.Close()

		// 创建表
		_, err = db.Exec(`CREATE TABLE IF NOT EXISTS vectors (
			id INTEGER PRIMARY KEY,
			content TEXT,
			embedding BLOB
		)`)
		if err != nil {
			log.Fatalf("Failed to create table: %v", err)
		}
		
		fmt.Println("✅ 向量数据库创建成功")
	} else {
		fmt.Println("✅ 向量数据库文件存在")
	}

	// 连接数据库
	db, err := sql.Open("sqlite3", cfg.VectorStore.Path)
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	// 检查表结构
	fmt.Println("\n📋 检查表结构...")
	rows, err := db.Query("PRAGMA table_info(vectors)")
	if err != nil {
		log.Fatalf("Failed to get table info: %v", err)
	}
	defer rows.Close()

	fmt.Println("表结构:")
	for rows.Next() {
		var cid int
		var name, dataType string
		var notNull, pk int
		var defaultValue sql.NullString

		if err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk); err != nil {
			log.Printf("Failed to scan row: %v", err)
			continue
		}

		fmt.Printf("  %d: %s (%s) NotNull=%d PK=%d\n", cid, name, dataType, notNull, pk)
	}

	// 检查数据
	fmt.Println("\n📊 检查向量数据...")
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM vectors").Scan(&count)
	if err != nil {
		log.Fatalf("Failed to count vectors: %v", err)
	}

	fmt.Printf("向量数据库中有 %d 条记录\n", count)

	if count > 0 {
		fmt.Println("\n📝 前5条记录:")
		rows2, err := db.Query("SELECT id, content, length(embedding) as embedding_size FROM vectors LIMIT 5")
		if err != nil {
			log.Printf("Failed to query vectors: %v", err)
			return
		}
		defer rows2.Close()

		for rows2.Next() {
			var id, embeddingSize int
			var content string

			if err := rows2.Scan(&id, &content, &embeddingSize); err != nil {
				log.Printf("Failed to scan vector row: %v", err)
				continue
			}

			fmt.Printf("  ID: %d, Content: %s, Embedding Size: %d bytes\n", id, content, embeddingSize)
		}
	} else {
		fmt.Println("⚠️  向量数据库为空，这就是为什么找不到答案的原因！")
	}

	fmt.Println("\n🔧 建议解决方案:")
	fmt.Println("1. 重新启动FAQ系统，让它重新生成向量")
	fmt.Println("2. 检查LocalAI服务是否正常运行")
	fmt.Println("3. 检查嵌入模型配置是否正确")
}
