@echo off
echo 🧪 测试C#问题修复...
echo.

echo 💻 1. 测试C#基本问题...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"C#是什么\"}"
echo.
echo.

echo 💻 2. 测试C#语言问题...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"C#是什么语言\"}"
echo.
echo.

echo 💻 3. 测试什么是C#...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是C#\"}"
echo.
echo.

echo 💻 4. 测试C#编程语言...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"C#编程语言\"}"
echo.
echo.

echo 🧠 5. 教学C#知识...
curl -X POST http://localhost:8081/api/knowledge/teach ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是C#？\",\"answer\":\"C#是微软开发的面向对象编程语言，运行在.NET框架上。\",\"user_id\":\"test_user\"}"
echo.
echo.

echo 🔍 6. 再次测试C#问题（应该使用学习知识）...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是C#\"}"
echo.
echo.

echo 📋 7. 获取学习知识列表...
curl -X GET http://localhost:8081/api/knowledge/list
echo.
echo.

echo 🎉 测试完成！
pause
