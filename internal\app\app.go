package app

import (
	"database/sql"
	"fmt"

	"faq-system/internal/config"
	"faq-system/internal/crawler"
	"faq-system/internal/database"
	"faq-system/internal/embedding"
	"faq-system/internal/health"
	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"faq-system/internal/mysql"
	"faq-system/internal/rag"
	"faq-system/internal/server"
	"faq-system/internal/vectorstore"
)

// Application 应用程序结构
type Application struct {
	Config      *config.Config
	Server      *server.Server
	RAGSystem   *rag.ChatSystem
	VectorStore *vectorstore.VectorStore
	EmbedClient *embedding.Client
	FAQs        []mysql.FAQ
	DB          *sql.DB
	Crawler     *crawler.KnowledgeCrawler
}

// New 创建新的应用程序实例
func New() (*Application, error) {
	app := &Application{}

	// 1. 初始化日志系统
	logger.Init()
	logger.Info("Starting FAQ System...")

	// 2. 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		// 如果配置文件不存在，使用默认配置
		logger.Warn("Config file not found, using default config")
		cfg = config.Load()
	}
	app.Config = cfg

	logger.Infof("Config loaded: LocalAI=%s, MySQL=%s:%s",
		cfg.LocalAI.BaseURL, cfg.MySQL.Host, cfg.MySQL.Port)

	// 3. 初始化数据库
	if err := app.initializeDatabase(); err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	// 4. 加载FAQ数据
	if err := app.loadFAQData(); err != nil {
		return nil, fmt.Errorf("failed to load FAQ data: %w", err)
	}

	// 5. 初始化向量存储
	if err := app.initializeVectorStore(); err != nil {
		return nil, fmt.Errorf("failed to initialize vector store: %w", err)
	}

	// 6. 初始化嵌入客户端
	app.initializeEmbeddingClient()

	// 7. 生成向量嵌入
	if err := app.generateEmbeddings(); err != nil {
		logger.Warnf("Failed to generate embeddings: %v", err)
	}

	// 8. 初始化RAG系统
	app.initializeRAGSystem()

	// 9. 初始化爬虫系统
	if err := app.initializeCrawler(); err != nil {
		logger.Warnf("Failed to initialize crawler: %v", err)
	}

	// 10. 初始化Web服务器
	if err := app.initializeServer(); err != nil {
		return nil, fmt.Errorf("failed to initialize server: %w", err)
	}

	return app, nil
}

// initializeDatabase 初始化数据库
func (app *Application) initializeDatabase() error {
	logger.Info("Initializing database...")
	return database.Initialize(app.Config)
}

// loadFAQData 加载FAQ数据
func (app *Application) loadFAQData() error {
	logger.Info("Loading FAQ data...")

	// 连接数据库
	db, err := mysql.Connect(app.Config)
	if err != nil {
		return err
	}
	app.DB = db

	// 加载FAQ数据
	faqs, err := mysql.LoadFAQsFromDB(db)
	if err != nil {
		return err
	}
	app.FAQs = faqs
	logger.Infof("Loaded %d FAQ entries", len(faqs))
	return nil
}

// initializeVectorStore 初始化向量存储
func (app *Application) initializeVectorStore() error {
	logger.Info("Initializing vector store...")
	store, err := vectorstore.NewVectorStore(app.Config.VectorStore.Path)
	if err != nil {
		return err
	}
	app.VectorStore = store
	return nil
}

// initializeEmbeddingClient 初始化嵌入客户端
func (app *Application) initializeEmbeddingClient() {
	logger.Info("Initializing embedding client...")
	app.EmbedClient = embedding.NewClient(app.Config.LocalAI.BaseURL, app.Config.LocalAI.EmbedModel)
}

// generateEmbeddings 生成向量嵌入
func (app *Application) generateEmbeddings() error {
	logger.Info("Generating embeddings for FAQ entries...")
	return app.VectorStore.GenerateEmbeddings(app.FAQs, app.EmbedClient)
}

// initializeRAGSystem 初始化RAG系统
func (app *Application) initializeRAGSystem() {
	logger.Info("Initializing RAG chat system...")
	app.RAGSystem = rag.NewChatSystem(app.VectorStore, app.EmbedClient, app.FAQs)
	logger.Info("RAG chat system initialized")
}

// initializeServer 初始化Web服务器
func (app *Application) initializeServer() error {
	logger.Info("Initializing web server...")

	// 创建健康检查器
	healthChecker := health.NewChecker(app.Config, app.VectorStore)

	// 创建学习管理器
	var learningManager *learning.Manager
	if app.DB != nil {
		learningManager = learning.NewManager(app.DB)
		// 启动学习系统
		if err := learningManager.Start(); err != nil {
			logger.Warnf("Failed to start learning system: %v", err)
		} else {
			logger.Info("Learning system started successfully")
			// 将学习引擎设置到RAG系统
			app.RAGSystem.SetLearningEngine(learningManager.GetEngine())

			// 创建并设置智能进化匹配器
			evolutionMatcher := learning.NewSmartMatcher(app.DB)
			app.RAGSystem.SetEvolutionMatcher(evolutionMatcher)
			logger.Info("🧠 智能进化匹配器已启用")

			// 创建并设置知识学习器
			knowledgeLearner := learningManager.GetKnowledgeLearner()
			app.RAGSystem.SetKnowledgeLearner(knowledgeLearner)
			logger.Info("📚 知识学习器已启用")
		}
	}

	// 创建服务器
	srv, err := server.New(app.Config, app.RAGSystem, healthChecker, learningManager, app.Crawler)
	if err != nil {
		return err
	}
	app.Server = srv
	return nil
}

// Run 运行应用程序
func (app *Application) Run() error {
	logger.Infof("Starting server on %s", app.Config.GetServerAddress())
	return app.Server.Start()
}

// Shutdown 关闭应用程序
func (app *Application) Shutdown() error {
	logger.Info("Shutting down application...")

	if app.VectorStore != nil {
		if err := app.VectorStore.Close(); err != nil {
			logger.Errorf("Failed to close vector store: %v", err)
		}
	}

	if app.Server != nil {
		return app.Server.Shutdown()
	}

	return nil
}

// initializeCrawler 初始化爬虫系统
func (app *Application) initializeCrawler() error {
	logger.Info("Initializing knowledge crawler...")

	// 创建学习管理器来获取知识学习器
	var knowledgeLearner *learning.KnowledgeLearner
	if app.DB != nil {
		learningManager := learning.NewManager(app.DB)
		knowledgeLearner = learningManager.GetKnowledgeLearner()
	}

	// 创建爬虫实例
	app.Crawler = crawler.NewKnowledgeCrawler(app.DB, knowledgeLearner)

	// 启动爬虫
	if err := app.Crawler.Start(); err != nil {
		return fmt.Errorf("failed to start crawler: %w", err)
	}

	logger.Info("✅ Knowledge crawler initialized and started")
	return nil
}
