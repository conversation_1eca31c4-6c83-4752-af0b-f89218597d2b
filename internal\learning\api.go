package learning

import (
	"encoding/json"
	"fmt"
	"net/http"

	"faq-system/internal/logger"
)

// FeedbackRequest 反馈请求
type FeedbackRequest struct {
	QueryID               int64  `json:"query_id"`
	ResponseID            int64  `json:"response_id"`
	FeedbackType          string `json:"feedback_type"` // helpful, not_helpful, partially_helpful
	Rating                *int   `json:"rating"`        // 1-5
	FeedbackText          string `json:"feedback_text"`
	ImprovementSuggestion string `json:"improvement_suggestion"`
}

// BehaviorRequest 行为记录请求
type BehaviorRequest struct {
	QueryID      int64        `json:"query_id"`
	BehaviorType string       `json:"behavior_type"` // click, scroll, copy, follow_up, exit
	BehaviorData BehaviorData `json:"behavior_data"`
}

// LearningAPI 学习API处理器
type LearningAPI struct {
	engine *LearningEngine
}

// NewLearningAPI 创建学习API处理器
func NewLearningAPI(engine *LearningEngine) *LearningAPI {
	return &LearningAPI{
		engine: engine,
	}
}

// HandleFeedback 处理用户反馈
func (api *LearningAPI) HandleFeedback(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req FeedbackRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// 验证请求
	if req.QueryID <= 0 || req.ResponseID <= 0 {
		http.Error(w, "Invalid query_id or response_id", http.StatusBadRequest)
		return
	}

	if req.FeedbackType == "" {
		http.Error(w, "feedback_type is required", http.StatusBadRequest)
		return
	}

	// 记录反馈
	err := api.engine.GetCollector().RecordFeedback(
		req.QueryID, req.ResponseID, req.FeedbackType,
		req.Rating, req.FeedbackText, req.ImprovementSuggestion)

	if err != nil {
		logger.Errorf("Failed to record feedback: %v", err)
		http.Error(w, "Failed to record feedback", http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	response := map[string]interface{}{
		"success": true,
		"message": "Feedback recorded successfully",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)

	logger.Infof("Recorded feedback: QueryID=%d, Type=%s", req.QueryID, req.FeedbackType)
}

// HandleBehavior 处理用户行为记录
func (api *LearningAPI) HandleBehavior(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req BehaviorRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// 验证请求
	if req.QueryID <= 0 {
		http.Error(w, "Invalid query_id", http.StatusBadRequest)
		return
	}

	if req.BehaviorType == "" {
		http.Error(w, "behavior_type is required", http.StatusBadRequest)
		return
	}

	// 记录行为
	err := api.engine.GetCollector().RecordBehavior(req.QueryID, req.BehaviorType, &req.BehaviorData)
	if err != nil {
		logger.Errorf("Failed to record behavior: %v", err)
		http.Error(w, "Failed to record behavior", http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	response := map[string]interface{}{
		"success": true,
		"message": "Behavior recorded successfully",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)

	logger.Infof("Recorded behavior: QueryID=%d, Type=%s", req.QueryID, req.BehaviorType)
}

// HandleMetrics 获取学习指标
func (api *LearningAPI) HandleMetrics(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	metrics, err := api.engine.GetCollector().GetLearningMetrics()
	if err != nil {
		logger.Errorf("Failed to get learning metrics: %v", err)
		http.Error(w, "Failed to get metrics", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(metrics)
}

// HandlePerformanceStats 获取性能统计
func (api *LearningAPI) HandlePerformanceStats(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	stats, err := api.getPerformanceStats()
	if err != nil {
		logger.Errorf("Failed to get performance stats: %v", err)
		http.Error(w, "Failed to get performance stats", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// HandleRecentActivity 获取最近活动
func (api *LearningAPI) HandleRecentActivity(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取limit参数
	limitStr := r.URL.Query().Get("limit")
	limit := 20 // 默认20条
	if limitStr != "" {
		if parsedLimit, err := json.Number(limitStr).Int64(); err == nil && parsedLimit > 0 {
			limit = int(parsedLimit)
		}
	}

	activities, err := api.getRecentActivity(limit)
	if err != nil {
		logger.Errorf("Failed to get recent activity: %v", err)
		// 返回空活动列表而不是错误，因为学习表可能还未创建
		activities = []map[string]interface{}{}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"activities": activities,
		"count":      len(activities),
	})
}

// HandleOptimizationRecommendations 获取优化建议
func (api *LearningAPI) HandleOptimizationRecommendations(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	optimizer := NewOptimizer(api.engine.db, api.engine)
	recommendations, err := optimizer.GetOptimizationRecommendations()
	if err != nil {
		logger.Errorf("Failed to get optimization recommendations: %v", err)
		http.Error(w, "Failed to get recommendations", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"recommendations": recommendations,
		"count":           len(recommendations),
	})
}

// HandleLearningAnalysis 触发学习分析
func (api *LearningAPI) HandleLearningAnalysis(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 异步执行学习分析
	go func() {
		if err := api.engine.AnalyzeQueryPatterns(); err != nil {
			logger.Errorf("Learning analysis failed: %v", err)
		}
	}()

	response := map[string]interface{}{
		"success": true,
		"message": "Learning analysis started",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// HandleLearningConfig 获取或更新学习配置
func (api *LearningAPI) HandleLearningConfig(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		api.getLearningConfig(w, r)
	case http.MethodPut:
		api.updateLearningConfig(w, r)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// getLearningConfig 获取学习配置
func (api *LearningAPI) getLearningConfig(w http.ResponseWriter, r *http.Request) {
	rows, err := api.engine.db.Query("SELECT config_key, config_value, config_type, description FROM learning_config")
	if err != nil {
		http.Error(w, "Failed to get config", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	configs := make(map[string]interface{})
	for rows.Next() {
		var key, value, configType, description string
		if err := rows.Scan(&key, &value, &configType, &description); err != nil {
			continue
		}

		configItem := map[string]interface{}{
			"value":       value,
			"type":        configType,
			"description": description,
		}

		configs[key] = configItem
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(configs)
}

// updateLearningConfig 更新学习配置
func (api *LearningAPI) updateLearningConfig(w http.ResponseWriter, r *http.Request) {
	var updates map[string]string
	if err := json.NewDecoder(r.Body).Decode(&updates); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	for key, value := range updates {
		_, err := api.engine.db.Exec(
			"UPDATE learning_config SET config_value = ?, updated_at = NOW() WHERE config_key = ?",
			value, key)
		if err != nil {
			logger.Errorf("Failed to update config %s: %v", key, err)
		}
	}

	// 重新加载配置
	api.engine.loadConfig()

	response := map[string]interface{}{
		"success": true,
		"message": fmt.Sprintf("Updated %d configuration items", len(updates)),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// getPerformanceStats 获取性能统计数据
func (api *LearningAPI) getPerformanceStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 1. 系统响应时间统计
	var avgResponseTime, minResponseTime, maxResponseTime float64
	err := api.engine.db.QueryRow(`
		SELECT
			AVG(processing_time_ms) as avg_time,
			MIN(processing_time_ms) as min_time,
			MAX(processing_time_ms) as max_time
		FROM system_responses
		WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
	`).Scan(&avgResponseTime, &minResponseTime, &maxResponseTime)

	if err != nil {
		// 如果没有数据，设置默认值
		avgResponseTime, minResponseTime, maxResponseTime = 0, 0, 0
	}

	stats["response_time"] = map[string]interface{}{
		"avg_ms": avgResponseTime,
		"min_ms": minResponseTime,
		"max_ms": maxResponseTime,
	}

	// 2. 查询量统计（按小时）
	hourlyQueries, err := api.getHourlyQueryStats()
	if err != nil {
		logger.Warnf("Failed to get hourly query stats: %v", err)
		hourlyQueries = []map[string]interface{}{}
	}
	stats["hourly_queries"] = hourlyQueries

	// 3. 意图分布统计
	intentStats, err := api.getIntentDistribution()
	if err != nil {
		logger.Warnf("Failed to get intent distribution: %v", err)
		intentStats = []map[string]interface{}{}
	}
	stats["intent_distribution"] = intentStats

	// 4. FAQ命中率统计
	faqHitRate, err := api.getFAQHitRate()
	if err != nil {
		logger.Warnf("Failed to get FAQ hit rate: %v", err)
		faqHitRate = []map[string]interface{}{}
	}
	stats["faq_hit_rate"] = faqHitRate

	// 5. 用户满意度趋势
	satisfactionTrend, err := api.getSatisfactionTrend()
	if err != nil {
		logger.Warnf("Failed to get satisfaction trend: %v", err)
		satisfactionTrend = []map[string]interface{}{}
	}
	stats["satisfaction_trend"] = satisfactionTrend

	return stats, nil
}

// getRecentActivity 获取最近活动数据
func (api *LearningAPI) getRecentActivity(limit int) ([]map[string]interface{}, error) {
	var activities []map[string]interface{}

	// 查询最近的用户查询、反馈和系统事件
	query := `
		SELECT
			'query' as activity_type,
			uq.query_text as content,
			uq.query_intent as metadata,
			uq.user_id as user_id,
			uq.created_at as timestamp
		FROM user_queries uq
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)

		UNION ALL

		SELECT
			'feedback' as activity_type,
			CONCAT('用户反馈: ', uf.feedback_type) as content,
			CAST(uf.rating AS CHAR) as metadata,
			'' as user_id,
			uf.created_at as timestamp
		FROM user_feedback uf
		WHERE uf.created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)

		UNION ALL

		SELECT
			'pattern' as activity_type,
			CONCAT('学习模式: ', lp.pattern_name) as content,
			CAST(lp.confidence AS CHAR) as metadata,
			'' as user_id,
			lp.last_updated as timestamp
		FROM learning_patterns lp
		WHERE lp.last_updated > DATE_SUB(NOW(), INTERVAL 24 HOUR)

		ORDER BY timestamp DESC
		LIMIT ?
	`

	rows, err := api.engine.db.Query(query, limit)
	if err != nil {
		return activities, err
	}
	defer rows.Close()

	for rows.Next() {
		var activityType, content, metadata, userID string
		var timestamp string

		if err := rows.Scan(&activityType, &content, &metadata, &userID, &timestamp); err != nil {
			continue
		}

		activity := map[string]interface{}{
			"type":      activityType,
			"content":   content,
			"metadata":  metadata,
			"user_id":   userID,
			"timestamp": timestamp,
		}

		activities = append(activities, activity)
	}

	return activities, nil
}

// RegisterRoutes 注册学习API路由
func (api *LearningAPI) RegisterRoutes(mux *http.ServeMux) {
	mux.HandleFunc("/api/learning/feedback", api.HandleFeedback)
	mux.HandleFunc("/api/learning/behavior", api.HandleBehavior)
	mux.HandleFunc("/api/learning/metrics", api.HandleMetrics)
	mux.HandleFunc("/api/learning/performance", api.HandlePerformanceStats)
	mux.HandleFunc("/api/learning/activity", api.HandleRecentActivity)
	mux.HandleFunc("/api/learning/recommendations", api.HandleOptimizationRecommendations)
	mux.HandleFunc("/api/learning/analyze", api.HandleLearningAnalysis)
	mux.HandleFunc("/api/learning/config", api.HandleLearningConfig)
}

// getHourlyQueryStats 获取按小时的查询统计
func (api *LearningAPI) getHourlyQueryStats() ([]map[string]interface{}, error) {
	query := `
		SELECT
			HOUR(created_at) as hour,
			COUNT(*) as query_count
		FROM user_queries
		WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
		GROUP BY HOUR(created_at)
		ORDER BY hour
	`

	rows, err := api.engine.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var stats []map[string]interface{}
	for rows.Next() {
		var hour, count int
		if err := rows.Scan(&hour, &count); err != nil {
			continue
		}
		stats = append(stats, map[string]interface{}{
			"hour":  hour,
			"count": count,
		})
	}

	return stats, nil
}

// getIntentDistribution 获取意图分布统计
func (api *LearningAPI) getIntentDistribution() ([]map[string]interface{}, error) {
	query := `
		SELECT
			COALESCE(query_intent, 'unknown') as intent,
			COUNT(*) as count,
			ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_queries WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)), 2) as percentage
		FROM user_queries
		WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
		GROUP BY query_intent
		ORDER BY count DESC
	`

	rows, err := api.engine.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var stats []map[string]interface{}
	for rows.Next() {
		var intent string
		var count int
		var percentage float64
		if err := rows.Scan(&intent, &count, &percentage); err != nil {
			continue
		}
		stats = append(stats, map[string]interface{}{
			"intent":     intent,
			"count":      count,
			"percentage": percentage,
		})
	}

	return stats, nil
}

// getFAQHitRate 获取FAQ命中率统计
func (api *LearningAPI) getFAQHitRate() ([]map[string]interface{}, error) {
	query := `
		SELECT
			f.id,
			f.question,
			COALESCE(fp.match_count, 0) as match_count,
			COALESCE(fp.positive_feedback, 0) as positive_feedback,
			COALESCE(fp.negative_feedback, 0) as negative_feedback,
			COALESCE(fp.avg_rating, 0) as avg_rating
		FROM faq f
		LEFT JOIN faq_performance fp ON f.id = fp.faq_id
		ORDER BY match_count DESC
		LIMIT 10
	`

	rows, err := api.engine.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var stats []map[string]interface{}
	for rows.Next() {
		var id, matchCount, positiveFeedback, negativeFeedback int
		var question string
		var avgRating float64
		if err := rows.Scan(&id, &question, &matchCount, &positiveFeedback, &negativeFeedback, &avgRating); err != nil {
			continue
		}

		// 计算命中率和满意度
		totalFeedback := positiveFeedback + negativeFeedback
		satisfactionRate := 0.0
		if totalFeedback > 0 {
			satisfactionRate = float64(positiveFeedback) / float64(totalFeedback) * 100
		}

		stats = append(stats, map[string]interface{}{
			"faq_id":            id,
			"question":          question,
			"match_count":       matchCount,
			"positive_feedback": positiveFeedback,
			"negative_feedback": negativeFeedback,
			"avg_rating":        avgRating,
			"satisfaction_rate": satisfactionRate,
		})
	}

	return stats, nil
}

// getSatisfactionTrend 获取用户满意度趋势
func (api *LearningAPI) getSatisfactionTrend() ([]map[string]interface{}, error) {
	query := `
		SELECT
			DATE(uf.created_at) as date,
			COUNT(*) as total_feedback,
			SUM(CASE WHEN uf.feedback_type = 'helpful' THEN 1 ELSE 0 END) as positive_count,
			SUM(CASE WHEN uf.feedback_type = 'not_helpful' THEN 1 ELSE 0 END) as negative_count,
			AVG(COALESCE(uf.rating, 3)) as avg_rating
		FROM user_feedback uf
		WHERE uf.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
		GROUP BY DATE(uf.created_at)
		ORDER BY date DESC
	`

	rows, err := api.engine.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var stats []map[string]interface{}
	for rows.Next() {
		var date string
		var totalFeedback, positiveCount, negativeCount int
		var avgRating float64
		if err := rows.Scan(&date, &totalFeedback, &positiveCount, &negativeCount, &avgRating); err != nil {
			continue
		}

		satisfactionRate := 0.0
		if totalFeedback > 0 {
			satisfactionRate = float64(positiveCount) / float64(totalFeedback) * 100
		}

		stats = append(stats, map[string]interface{}{
			"date":              date,
			"total_feedback":    totalFeedback,
			"positive_count":    positiveCount,
			"negative_count":    negativeCount,
			"avg_rating":        avgRating,
			"satisfaction_rate": satisfactionRate,
		})
	}

	return stats, nil
}
