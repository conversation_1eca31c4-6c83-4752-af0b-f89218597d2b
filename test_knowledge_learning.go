package main

import (
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/learning"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🧠 测试知识学习功能...")

	// 创建知识学习器
	knowledgeLearner := learning.NewKnowledgeLearner(db, nil, nil)

	// 测试用例
	testCases := []struct {
		query        string
		userResponse string
		description  string
	}{
		{
			query:        "C#是一种编程语言",
			userResponse: "",
			description:  "直接知识声明",
		},
		{
			query:        "什么是Python？",
			userResponse: "Python是一种高级编程语言，以其简洁的语法著称",
			description:  "问答形式的知识",
		},
		{
			query:        "Java是什么",
			userResponse: "",
			description:  "问句形式",
		},
		{
			query:        "Go语言是一种编程语言",
			userResponse: "",
			description:  "另一个直接声明",
		},
	}

	for i, testCase := range testCases {
		fmt.Printf("\n测试 %d: %s\n", i+1, testCase.description)
		fmt.Printf("查询: %s\n", testCase.query)
		fmt.Printf("用户回应: %s\n", testCase.userResponse)

		// 尝试学习知识
		err := knowledgeLearner.LearnFromUserInput("test_user", testCase.query, testCase.userResponse, "")
		if err != nil {
			fmt.Printf("❌ 学习失败: %v\n", err)
		} else {
			fmt.Printf("✅ 学习处理完成\n")
		}
	}

	// 查看学习到的知识
	fmt.Println("\n📚 查看学习到的知识:")
	pendingKnowledge, err := knowledgeLearner.GetPendingKnowledge(10)
	if err != nil {
		fmt.Printf("❌ 获取待审核知识失败: %v\n", err)
	} else {
		fmt.Printf("发现 %d 条待审核知识:\n", len(pendingKnowledge))
		for i, knowledge := range pendingKnowledge {
			fmt.Printf("  %d. 问题: %s\n", i+1, knowledge.Question)
			fmt.Printf("     答案: %s\n", knowledge.Answer)
			fmt.Printf("     来源: %s\n", knowledge.Source)
			fmt.Printf("     置信度: %.2f\n", knowledge.Confidence)
			fmt.Printf("     状态: %s\n", knowledge.Status)
			fmt.Println()
		}
	}

	// 测试搜索功能
	fmt.Println("🔍 测试知识搜索:")
	searchResults, err := knowledgeLearner.SearchLearnedKnowledge("C#", 5)
	if err != nil {
		fmt.Printf("❌ 搜索失败: %v\n", err)
	} else {
		fmt.Printf("搜索 'C#' 找到 %d 条结果:\n", len(searchResults))
		for i, result := range searchResults {
			fmt.Printf("  %d. %s -> %s (置信度: %.2f)\n", 
				i+1, result.Question, result.Answer, result.Confidence)
		}
	}

	fmt.Println("\n🎉 知识学习测试完成！")
}
