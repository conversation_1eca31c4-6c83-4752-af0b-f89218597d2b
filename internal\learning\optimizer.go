package learning

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"sort"

	"faq-system/internal/logger"
)

// Optimizer 学习优化器
type Optimizer struct {
	db     *sql.DB
	engine *LearningEngine
}

// NewOptimizer 创建优化器
func NewOptimizer(db *sql.DB, engine *LearningEngine) *Optimizer {
	return &Optimizer{
		db:     db,
		engine: engine,
	}
}

// OptimizeMatchingScores 优化匹配得分
func (o *Optimizer) OptimizeMatchingScores() error {
	if !o.engine.getBoolConfig("auto_optimize_enabled", true) {
		return nil
	}

	logger.Info("Starting matching score optimization...")

	// 获取学习到的模式
	patterns, err := o.getLearningPatterns("query_similarity")
	if err != nil {
		return err
	}

	// 应用相似查询模式优化
	for _, pattern := range patterns {
		if err := o.applySimilarQueryOptimization(pattern); err != nil {
			logger.Warnf("Failed to apply similar query optimization: %v", err)
		}
	}

	// 获取意图映射模式
	intentPatterns, err := o.getLearningPatterns("intent_mapping")
	if err != nil {
		return err
	}

	// 应用意图映射优化
	for _, pattern := range intentPatterns {
		if err := o.applyIntentMappingOptimization(pattern); err != nil {
			logger.Warnf("Failed to apply intent mapping optimization: %v", err)
		}
	}

	logger.Info("Matching score optimization completed")
	return nil
}

// getLearningPatterns 获取学习模式
func (o *Optimizer) getLearningPatterns(patternType string) ([]LearningPattern, error) {
	query := `
		SELECT id, pattern_type, pattern_name, pattern_data, confidence, usage_count, success_rate
		FROM learning_patterns
		WHERE pattern_type = ? AND confidence > 0.5
		ORDER BY confidence DESC, usage_count DESC
		LIMIT 50
	`

	rows, err := o.db.Query(query, patternType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var patterns []LearningPattern
	for rows.Next() {
		var pattern LearningPattern
		err := rows.Scan(&pattern.ID, &pattern.PatternType, &pattern.PatternName,
			&pattern.PatternData, &pattern.Confidence, &pattern.UsageCount, &pattern.SuccessRate)
		if err != nil {
			continue
		}
		patterns = append(patterns, pattern)
	}

	return patterns, nil
}

// applySimilarQueryOptimization 应用相似查询优化
func (o *Optimizer) applySimilarQueryOptimization(pattern LearningPattern) error {
	var patternData PatternData
	if err := json.Unmarshal(pattern.PatternData, &patternData); err != nil {
		return err
	}

	// 为相似查询创建优化建议
	for _, similarQuery := range patternData.SimilarQueries {
		// 检查这个查询的历史表现
		performance, err := o.getQueryPerformance(similarQuery.Query, similarQuery.FAQID)
		if err != nil {
			continue
		}

		// 如果表现不佳，创建优化建议
		if performance.AvgRating < 3.0 || performance.NegativeFeedback > performance.PositiveFeedback {
			suggestion := o.generateOptimizationSuggestion(similarQuery, performance)
			o.saveOptimizationSuggestion(suggestion)
		}
	}

	return nil
}

// getQueryPerformance 获取查询性能
func (o *Optimizer) getQueryPerformance(queryText string, faqID int) (*QueryPerformance, error) {
	query := `
		SELECT 
			COUNT(DISTINCT uq.id) as query_count,
			AVG(COALESCE(uf.rating, 3)) as avg_rating,
			SUM(CASE WHEN uf.feedback_type = 'helpful' THEN 1 ELSE 0 END) as positive_feedback,
			SUM(CASE WHEN uf.feedback_type = 'not_helpful' THEN 1 ELSE 0 END) as negative_feedback,
			AVG(sr.confidence_score) as avg_confidence
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		LEFT JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE uq.query_text LIKE ? AND sr.matched_faq_id = ?
	`

	var perf QueryPerformance
	err := o.db.QueryRow(query, "%"+queryText+"%", faqID).Scan(
		&perf.QueryCount, &perf.AvgRating, &perf.PositiveFeedback,
		&perf.NegativeFeedback, &perf.AvgConfidence)

	return &perf, err
}

// QueryPerformance 查询性能
type QueryPerformance struct {
	QueryCount       int     `json:"query_count"`
	AvgRating        float32 `json:"avg_rating"`
	PositiveFeedback int     `json:"positive_feedback"`
	NegativeFeedback int     `json:"negative_feedback"`
	AvgConfidence    float32 `json:"avg_confidence"`
}

// generateOptimizationSuggestion 生成优化建议
func (o *Optimizer) generateOptimizationSuggestion(similarQuery SimilarQuery, performance *QueryPerformance) OptimizationSuggestion {
	suggestion := OptimizationSuggestion{
		QueryText:    similarQuery.Query,
		FAQID:        similarQuery.FAQID,
		CurrentScore: performance.AvgConfidence,
		Issues:       []string{},
		Suggestions:  []string{},
	}

	// 分析问题
	if performance.AvgRating < 2.0 {
		suggestion.Issues = append(suggestion.Issues, "用户评分过低")
		suggestion.Suggestions = append(suggestion.Suggestions, "考虑改进FAQ答案内容")
	}

	if performance.NegativeFeedback > performance.PositiveFeedback*2 {
		suggestion.Issues = append(suggestion.Issues, "负面反馈过多")
		suggestion.Suggestions = append(suggestion.Suggestions, "检查答案是否准确回答了问题")
	}

	if performance.AvgConfidence < 0.6 {
		suggestion.Issues = append(suggestion.Issues, "匹配置信度过低")
		suggestion.Suggestions = append(suggestion.Suggestions, "优化关键词匹配或增加相似问题")
	}

	// 计算建议的新得分
	suggestion.SuggestedScore = o.calculateOptimizedScore(performance)

	return suggestion
}

// OptimizationSuggestion 优化建议
type OptimizationSuggestion struct {
	QueryText      string   `json:"query_text"`
	FAQID          int      `json:"faq_id"`
	CurrentScore   float32  `json:"current_score"`
	SuggestedScore float32  `json:"suggested_score"`
	Issues         []string `json:"issues"`
	Suggestions    []string `json:"suggestions"`
	Priority       int      `json:"priority"`
}

// calculateOptimizedScore 计算优化后的得分
func (o *Optimizer) calculateOptimizedScore(performance *QueryPerformance) float32 {
	// 基于反馈调整得分
	feedbackRatio := float32(performance.PositiveFeedback) / float32(math.Max(1, float64(performance.PositiveFeedback+performance.NegativeFeedback)))
	ratingFactor := performance.AvgRating / 5.0

	// 综合计算优化得分
	optimizedScore := performance.AvgConfidence*0.6 + feedbackRatio*0.3 + ratingFactor*0.1

	return float32(math.Min(1.0, float64(optimizedScore)))
}

// saveOptimizationSuggestion 保存优化建议
func (o *Optimizer) saveOptimizationSuggestion(suggestion OptimizationSuggestion) error {
	suggestionJSON, err := json.Marshal(suggestion)
	if err != nil {
		return err
	}

	query := `
		INSERT INTO learning_patterns (pattern_type, pattern_name, pattern_data, confidence)
		VALUES ('response_optimization', ?, ?, ?)
		ON DUPLICATE KEY UPDATE
		pattern_data = VALUES(pattern_data),
		confidence = VALUES(confidence),
		last_updated = NOW()
	`

	patternName := fmt.Sprintf("optimization_faq_%d", suggestion.FAQID)
	confidence := suggestion.SuggestedScore

	_, err = o.db.Exec(query, patternName, suggestionJSON, confidence)
	return err
}

// applyIntentMappingOptimization 应用意图映射优化
func (o *Optimizer) applyIntentMappingOptimization(pattern LearningPattern) error {
	var patternData PatternData
	if err := json.Unmarshal(pattern.PatternData, &patternData); err != nil {
		return err
	}

	// 分析意图映射的效果
	for faqIDStr, weight := range patternData.IntentMapping {
		var faqID int
		fmt.Sscanf(faqIDStr, "%d", &faqID)

		// 获取该FAQ的实际表现
		actualPerformance, err := o.getFAQPerformance(faqID)
		if err != nil {
			continue
		}

		// 如果预期权重与实际表现不符，调整权重
		expectedPerformance := weight
		actualScore := o.calculateFAQScore(actualPerformance)

		if math.Abs(float64(expectedPerformance-actualScore)) > 0.3 {
			// 需要调整映射权重
			adjustedWeight := (expectedPerformance + actualScore) / 2
			patternData.IntentMapping[faqIDStr] = adjustedWeight

			logger.Infof("Adjusted intent mapping weight for FAQ %d: %.3f -> %.3f",
				faqID, expectedPerformance, adjustedWeight)
		}
	}

	// 保存调整后的模式
	adjustedDataJSON, err := json.Marshal(patternData)
	if err != nil {
		return err
	}

	updateQuery := `
		UPDATE learning_patterns 
		SET pattern_data = ?, last_updated = NOW()
		WHERE id = ?
	`

	_, err = o.db.Exec(updateQuery, adjustedDataJSON, pattern.ID)
	return err
}

// getFAQPerformance 获取FAQ性能
func (o *Optimizer) getFAQPerformance(faqID int) (*FAQPerformance, error) {
	query := `
		SELECT faq_id, query_count, match_count, positive_feedback, negative_feedback,
			   avg_confidence, avg_rating
		FROM faq_performance
		WHERE faq_id = ?
	`

	var perf FAQPerformance
	err := o.db.QueryRow(query, faqID).Scan(
		&perf.FAQID, &perf.QueryCount, &perf.MatchCount,
		&perf.PositiveFeedback, &perf.NegativeFeedback,
		&perf.AvgConfidence, &perf.AvgRating)

	return &perf, err
}

// calculateFAQScore 计算FAQ综合得分
func (o *Optimizer) calculateFAQScore(perf *FAQPerformance) float32 {
	if perf.MatchCount == 0 {
		return 0
	}

	// 综合考虑置信度、评分和反馈比例
	feedbackRatio := float32(perf.PositiveFeedback) / float32(math.Max(1, float64(perf.PositiveFeedback+perf.NegativeFeedback)))
	ratingScore := perf.AvgRating / 5.0

	score := perf.AvgConfidence*0.4 + feedbackRatio*0.4 + ratingScore*0.2
	return float32(math.Min(1.0, float64(score)))
}

// GetOptimizationRecommendations 获取优化建议
func (o *Optimizer) GetOptimizationRecommendations() ([]OptimizationSuggestion, error) {
	var recommendations []OptimizationSuggestion

	// 1. 基于FAQ性能生成建议
	faqRecommendations, err := o.generateFAQRecommendations()
	if err == nil {
		recommendations = append(recommendations, faqRecommendations...)
	}

	// 2. 基于用户反馈生成建议
	feedbackRecommendations, err := o.generateFeedbackRecommendations()
	if err == nil {
		recommendations = append(recommendations, feedbackRecommendations...)
	}

	// 3. 基于系统性能生成建议
	performanceRecommendations, err := o.generatePerformanceRecommendations()
	if err == nil {
		recommendations = append(recommendations, performanceRecommendations...)
	}

	// 4. 从学习模式中获取存储的建议
	storedRecommendations, err := o.getStoredRecommendations()
	if err == nil {
		recommendations = append(recommendations, storedRecommendations...)
	}

	// 按优先级排序
	sort.Slice(recommendations, func(i, j int) bool {
		return recommendations[i].Priority > recommendations[j].Priority
	})

	// 限制返回数量
	if len(recommendations) > 20 {
		recommendations = recommendations[:20]
	}

	return recommendations, nil
}

// generateFAQRecommendations 基于FAQ性能生成建议
func (o *Optimizer) generateFAQRecommendations() ([]OptimizationSuggestion, error) {
	var recommendations []OptimizationSuggestion

	// 查找表现不佳的FAQ
	query := `
		SELECT
			fp.faq_id,
			f.question,
			fp.match_count,
			fp.positive_feedback,
			fp.negative_feedback,
			fp.avg_rating,
			fp.avg_confidence
		FROM faq_performance fp
		JOIN faq f ON fp.faq_id = f.id
		WHERE fp.match_count > 5
		  AND (fp.avg_rating < 3.0 OR fp.negative_feedback > fp.positive_feedback)
		ORDER BY (fp.negative_feedback - fp.positive_feedback) DESC
		LIMIT 10
	`

	rows, err := o.db.Query(query)
	if err != nil {
		return recommendations, err
	}
	defer rows.Close()

	for rows.Next() {
		var faqID, matchCount, positiveFeedback, negativeFeedback int
		var question string
		var avgRating, avgConfidence float64

		if err := rows.Scan(&faqID, &question, &matchCount, &positiveFeedback, &negativeFeedback, &avgRating, &avgConfidence); err != nil {
			continue
		}

		suggestion := OptimizationSuggestion{
			QueryText:      question,
			FAQID:          faqID,
			CurrentScore:   float32(avgConfidence),
			SuggestedScore: float32(avgConfidence * 1.2), // 建议提升20%
			Issues:         []string{},
			Suggestions:    []string{},
			Priority:       calculatePriority(matchCount, positiveFeedback, negativeFeedback, avgRating),
		}

		// 分析具体问题
		if avgRating < 2.0 {
			suggestion.Issues = append(suggestion.Issues, "用户评分过低")
			suggestion.Suggestions = append(suggestion.Suggestions, "重新审查和改进FAQ答案内容")
		}

		if negativeFeedback > positiveFeedback*2 {
			suggestion.Issues = append(suggestion.Issues, "负面反馈过多")
			suggestion.Suggestions = append(suggestion.Suggestions, "检查答案是否准确回答了问题")
		}

		if avgConfidence < 0.6 {
			suggestion.Issues = append(suggestion.Issues, "匹配置信度过低")
			suggestion.Suggestions = append(suggestion.Suggestions, "优化关键词匹配或增加相似问题")
		}

		recommendations = append(recommendations, suggestion)
	}

	return recommendations, nil
}

// generateFeedbackRecommendations 基于用户反馈生成建议
func (o *Optimizer) generateFeedbackRecommendations() ([]OptimizationSuggestion, error) {
	var recommendations []OptimizationSuggestion

	// 查找最近收到负面反馈的查询
	query := `
		SELECT
			uq.query_text,
			COUNT(*) as negative_count,
			GROUP_CONCAT(uf.feedback_text SEPARATOR '; ') as feedback_texts
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE uf.feedback_type = 'not_helpful'
		  AND uf.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
		GROUP BY uq.query_text
		HAVING negative_count >= 2
		ORDER BY negative_count DESC
		LIMIT 5
	`

	rows, err := o.db.Query(query)
	if err != nil {
		return recommendations, err
	}
	defer rows.Close()

	for rows.Next() {
		var queryText, feedbackTexts string
		var negativeCount int

		if err := rows.Scan(&queryText, &negativeCount, &feedbackTexts); err != nil {
			continue
		}

		suggestion := OptimizationSuggestion{
			QueryText:      queryText,
			FAQID:          0, // 通用建议
			CurrentScore:   0.3,
			SuggestedScore: 0.7,
			Issues:         []string{fmt.Sprintf("收到%d条负面反馈", negativeCount)},
			Suggestions:    []string{"分析用户反馈，改进相关FAQ或添加新FAQ", "考虑添加更详细的解释或示例"},
			Priority:       negativeCount * 10, // 负面反馈越多优先级越高
		}

		recommendations = append(recommendations, suggestion)
	}

	return recommendations, nil
}

// generatePerformanceRecommendations 基于系统性能生成建议
func (o *Optimizer) generatePerformanceRecommendations() ([]OptimizationSuggestion, error) {
	var recommendations []OptimizationSuggestion

	// 检查响应时间
	var avgResponseTime float64
	err := o.db.QueryRow(`
		SELECT AVG(processing_time_ms)
		FROM system_responses
		WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
	`).Scan(&avgResponseTime)

	if err == nil && avgResponseTime > 1000 { // 超过1秒
		suggestion := OptimizationSuggestion{
			QueryText:      "系统性能优化",
			FAQID:          0,
			CurrentScore:   0.5,
			SuggestedScore: 0.8,
			Issues:         []string{fmt.Sprintf("平均响应时间过长: %.1fms", avgResponseTime)},
			Suggestions:    []string{"优化数据库查询", "考虑添加缓存机制", "优化向量搜索算法"},
			Priority:       int(avgResponseTime / 100), // 响应时间越长优先级越高
		}
		recommendations = append(recommendations, suggestion)
	}

	return recommendations, nil
}

// getStoredRecommendations 获取存储的优化建议
func (o *Optimizer) getStoredRecommendations() ([]OptimizationSuggestion, error) {
	query := `
		SELECT pattern_data
		FROM learning_patterns
		WHERE pattern_type = 'response_optimization'
		  AND confidence > 0.3
		ORDER BY confidence DESC
		LIMIT 10
	`

	rows, err := o.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var recommendations []OptimizationSuggestion
	for rows.Next() {
		var dataJSON string
		if err := rows.Scan(&dataJSON); err != nil {
			continue
		}

		var suggestion OptimizationSuggestion
		if err := json.Unmarshal([]byte(dataJSON), &suggestion); err != nil {
			continue
		}

		recommendations = append(recommendations, suggestion)
	}

	return recommendations, nil
}

// calculatePriority 计算优化建议的优先级
func calculatePriority(matchCount, positiveFeedback, negativeFeedback int, avgRating float64) int {
	// 基于匹配次数、反馈比例和评分计算优先级
	feedbackRatio := float64(negativeFeedback) / float64(math.Max(1, float64(positiveFeedback+negativeFeedback)))
	ratingFactor := (5.0 - avgRating) / 5.0 // 评分越低因子越大

	priority := int(float64(matchCount) * feedbackRatio * ratingFactor * 10)
	return priority
}
