package main

import (
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🔧 创建智能进化相关表...")

	// 创建用户思维模式表
	createUserThinkingPatternsTable := `
		CREATE TABLE IF NOT EXISTS user_thinking_patterns (
			id INT AUTO_INCREMENT PRIMARY KEY,
			pattern_type ENUM('query_style', 'intent_preference', 'feedback_pattern', 'emerging_pattern') NOT NULL COMMENT '模式类型',
			pattern_name VARCHAR(200) NOT NULL COMMENT '模式名称',
			user_segment ENUM('technical', 'business', 'general') DEFAULT 'general' COMMENT '用户细分',
			thinking_style VARCHAR(100) COMMENT '思维风格',
			query_patterns JSON COMMENT '查询模式',
			preferred_topics JSON COMMENT '偏好话题',
			response_style VARCHAR(100) COMMENT '偏好的响应风格',
			confidence FLOAT DEFAULT 0.0 COMMENT '模式置信度',
			usage_count INT DEFAULT 0 COMMENT '使用次数',
			success_rate FLOAT DEFAULT 0.0 COMMENT '成功率',
			metadata JSON COMMENT '元数据',
			last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_pattern_type (pattern_type),
			INDEX idx_user_segment (user_segment),
			INDEX idx_success_rate (success_rate),
			INDEX idx_usage_count (usage_count),
			UNIQUE KEY uk_pattern_name (pattern_name)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户思维模式表'
	`

	// 创建系统进化历史表
	createSystemEvolutionTable := `
		CREATE TABLE IF NOT EXISTS system_evolution (
			id INT AUTO_INCREMENT PRIMARY KEY,
			evolution_type ENUM('matching_improvement', 'response_optimization', 'new_pattern_discovery', 'performance_optimization', 'system_initialization') NOT NULL COMMENT '进化类型',
			description TEXT NOT NULL COMMENT '进化描述',
			before_metrics JSON COMMENT '改进前指标',
			after_metrics JSON COMMENT '改进后指标',
			improvement_rate FLOAT DEFAULT 0.0 COMMENT '改进幅度',
			applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '应用时间',
			status ENUM('active', 'testing', 'deprecated') DEFAULT 'active' COMMENT '状态',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_evolution_type (evolution_type),
			INDEX idx_improvement_rate (improvement_rate),
			INDEX idx_applied_at (applied_at),
			INDEX idx_status (status)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统进化历史表'
	`

	// 创建智能匹配规则表
	createSmartMatchingRulesTable := `
		CREATE TABLE IF NOT EXISTS smart_matching_rules (
			id INT AUTO_INCREMENT PRIMARY KEY,
			rule_name VARCHAR(200) NOT NULL COMMENT '规则名称',
			rule_type ENUM('keyword_boost', 'intent_mapping', 'context_aware', 'user_preference') NOT NULL COMMENT '规则类型',
			conditions JSON NOT NULL COMMENT '触发条件',
			actions JSON NOT NULL COMMENT '执行动作',
			priority INT DEFAULT 0 COMMENT '优先级',
			effectiveness_score FLOAT DEFAULT 0.0 COMMENT '效果评分',
			usage_count INT DEFAULT 0 COMMENT '使用次数',
			success_rate FLOAT DEFAULT 0.0 COMMENT '成功率',
			is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
			created_by VARCHAR(100) DEFAULT 'system' COMMENT '创建者',
			last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_rule_type (rule_type),
			INDEX idx_priority (priority),
			INDEX idx_effectiveness (effectiveness_score),
			INDEX idx_is_active (is_active),
			UNIQUE KEY uk_rule_name (rule_name)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='智能匹配规则表'
	`

	// 执行表创建
	tables := map[string]string{
		"user_thinking_patterns": createUserThinkingPatternsTable,
		"system_evolution":       createSystemEvolutionTable,
		"smart_matching_rules":   createSmartMatchingRulesTable,
	}

	for tableName, createSQL := range tables {
		fmt.Printf("📋 创建表: %s\n", tableName)
		if _, err := db.Exec(createSQL); err != nil {
			log.Fatalf("Failed to create table %s: %v", tableName, err)
		}
		fmt.Printf("✅ 表 %s 创建成功\n", tableName)
	}

	// 插入初始智能匹配规则
	fmt.Println("📝 插入初始智能匹配规则...")
	initialRules := []string{
		`INSERT IGNORE INTO smart_matching_rules (rule_name, rule_type, conditions, actions, priority, effectiveness_score) VALUES 
		('technical_keyword_boost', 'keyword_boost', 
		 '{"keywords": ["API", "数据库", "算法", "向量", "MySQL", "Go", "LocalAI"], "boost_factor": 1.2}',
		 '{"action": "boost_confidence", "parameters": {"factor": 1.2}}',
		 10, 0.8)`,

		`INSERT IGNORE INTO smart_matching_rules (rule_name, rule_type, conditions, actions, priority, effectiveness_score) VALUES 
		('direct_question_mapping', 'intent_mapping',
		 '{"intent": "technical_question", "confidence_threshold": 0.6}',
		 '{"action": "prefer_detailed_answer", "parameters": {"detail_level": "high"}}',
		 8, 0.75)`,

		`INSERT IGNORE INTO smart_matching_rules (rule_name, rule_type, conditions, actions, priority, effectiveness_score) VALUES 
		('user_preference_adaptation', 'user_preference',
		 '{"user_segment": "technical", "thinking_style": "direct"}',
		 '{"action": "adjust_response_style", "parameters": {"style": "concise", "include_examples": true}}',
		 6, 0.7)`,

		`INSERT IGNORE INTO smart_matching_rules (rule_name, rule_type, conditions, actions, priority, effectiveness_score) VALUES 
		('context_aware_followup', 'context_aware',
		 '{"previous_queries": true, "session_length": ">1"}',
		 '{"action": "consider_context", "parameters": {"context_weight": 0.3}}',
		 5, 0.65)`,
	}

	for i, ruleSQL := range initialRules {
		if _, err := db.Exec(ruleSQL); err != nil {
			log.Printf("Warning: Failed to insert initial rule %d: %v", i+1, err)
		} else {
			fmt.Printf("✅ 插入初始规则 %d/4\n", i+1)
		}
	}

	// 插入初始系统进化记录
	fmt.Println("📈 记录系统初始化...")
	initEvolutionSQL := `
		INSERT IGNORE INTO system_evolution (evolution_type, description, before_metrics, after_metrics, improvement_rate) VALUES 
		('system_initialization', '🚀 智能进化系统初始化完成', '{}', 
		 '{"timestamp": "` + fmt.Sprintf("%s", "2025-01-01 00:00:00") + `", "status": "initialized"}', 0.0)
	`

	if _, err := db.Exec(initEvolutionSQL); err != nil {
		log.Printf("Warning: Failed to insert initial evolution record: %v", err)
	} else {
		fmt.Println("✅ 系统初始化记录创建成功")
	}

	fmt.Println("\n🎉 智能进化系统数据库初始化完成！")
	fmt.Println("📊 创建的表:")
	fmt.Println("  • user_thinking_patterns - 用户思维模式分析")
	fmt.Println("  • system_evolution - 系统进化历史追踪")
	fmt.Println("  • smart_matching_rules - 智能匹配规则引擎")
	fmt.Println("\n🧠 系统现在具备真正的学习和进化能力！")
}
