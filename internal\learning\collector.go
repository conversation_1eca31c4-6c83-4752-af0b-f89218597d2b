package learning

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"faq-system/internal/logger"
)

// DataCollector 数据收集器
type DataCollector struct {
	db     *sql.DB
	config *LearningConfig
}

// NewDataCollector 创建数据收集器
func NewDataCollector(db *sql.DB) *DataCollector {
	return &DataCollector{
		db: db,
	}
}

// RecordQuery 记录用户查询
func (dc *DataCollector) RecordQuery(sessionID, userID, queryText, intent, queryType string, embedding []float32, context *QueryContext) (int64, error) {
	// 序列化embedding
	embeddingJSON, err := json.Marshal(embedding)
	if err != nil {
		logger.Warnf("Failed to marshal embedding: %v", err)
		embeddingJSON = []byte("[]")
	}

	// 序列化上下文
	contextJSON, err := json.Marshal(context)
	if err != nil {
		logger.Warnf("Failed to marshal context: %v", err)
		contextJSON = []byte("{}")
	}

	query := `
		INSERT INTO user_queries (session_id, user_id, query_text, query_intent, query_type, query_embedding, context_data)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	result, err := dc.db.Exec(query, sessionID, userID, queryText, intent, queryType, embeddingJSON, contextJSON)
	if err != nil {
		return 0, fmt.Errorf("failed to record query: %v", err)
	}

	queryID, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get query ID: %v", err)
	}

	logger.Infof("Recorded query: ID=%d, User=%s, Intent=%s", queryID, userID, intent)
	return queryID, nil
}

// RecordResponse 记录系统响应
func (dc *DataCollector) RecordResponse(queryID int64, faqID *int, responseText, source, matchType string, confidence *float32, processingTime int) (int64, error) {
	query := `
		INSERT INTO system_responses (query_id, matched_faq_id, response_text, response_source, confidence_score, match_type, processing_time_ms)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	result, err := dc.db.Exec(query, queryID, faqID, responseText, source, confidence, matchType, processingTime)
	if err != nil {
		return 0, fmt.Errorf("failed to record response: %v", err)
	}

	responseID, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get response ID: %v", err)
	}

	// 更新FAQ性能统计
	if faqID != nil {
		dc.updateFAQPerformance(*faqID, confidence)
	}

	logger.Infof("Recorded response: ID=%d, QueryID=%d, Confidence=%.3f", responseID, queryID, getFloat32Value(confidence))
	return responseID, nil
}

// RecordFeedback 记录用户反馈
func (dc *DataCollector) RecordFeedback(queryID, responseID int64, feedbackType string, rating *int, feedbackText, suggestion string) error {
	query := `
		INSERT INTO user_feedback (query_id, response_id, feedback_type, rating, feedback_text, improvement_suggestion)
		VALUES (?, ?, ?, ?, ?, ?)
	`

	_, err := dc.db.Exec(query, queryID, responseID, feedbackType, rating, feedbackText, suggestion)
	if err != nil {
		return fmt.Errorf("failed to record feedback: %v", err)
	}

	// 更新FAQ性能统计中的反馈数据
	dc.updateFAQFeedback(responseID, feedbackType, rating)

	logger.Infof("Recorded feedback: QueryID=%d, ResponseID=%d, Type=%s", queryID, responseID, feedbackType)
	return nil
}

// RecordBehavior 记录用户行为
func (dc *DataCollector) RecordBehavior(queryID int64, behaviorType string, behaviorData *BehaviorData) error {
	dataJSON, err := json.Marshal(behaviorData)
	if err != nil {
		logger.Warnf("Failed to marshal behavior data: %v", err)
		dataJSON = []byte("{}")
	}

	query := `
		INSERT INTO user_behaviors (query_id, behavior_type, behavior_data, timestamp_ms)
		VALUES (?, ?, ?, ?)
	`

	_, err = dc.db.Exec(query, queryID, behaviorType, dataJSON, time.Now().UnixMilli())
	if err != nil {
		return fmt.Errorf("failed to record behavior: %v", err)
	}

	logger.Infof("Recorded behavior: QueryID=%d, Type=%s", queryID, behaviorType)
	return nil
}

// updateFAQPerformance 更新FAQ性能统计
func (dc *DataCollector) updateFAQPerformance(faqID int, confidence *float32) {
	// 检查记录是否存在
	var exists bool
	checkQuery := "SELECT EXISTS(SELECT 1 FROM faq_performance WHERE faq_id = ?)"
	dc.db.QueryRow(checkQuery, faqID).Scan(&exists)

	if !exists {
		// 创建新记录
		insertQuery := `
			INSERT INTO faq_performance (faq_id, query_count, match_count, avg_confidence, last_matched)
			VALUES (?, 1, 1, ?, NOW())
		`
		dc.db.Exec(insertQuery, faqID, getFloat32Value(confidence))
	} else {
		// 更新现有记录
		updateQuery := `
			UPDATE faq_performance 
			SET match_count = match_count + 1,
				avg_confidence = (avg_confidence * (match_count - 1) + ?) / match_count,
				last_matched = NOW(),
				updated_at = NOW()
			WHERE faq_id = ?
		`
		dc.db.Exec(updateQuery, getFloat32Value(confidence), faqID)
	}
}

// updateFAQFeedback 更新FAQ反馈统计
func (dc *DataCollector) updateFAQFeedback(responseID int64, feedbackType string, rating *int) {
	// 获取响应对应的FAQ ID
	var faqID *int
	query := "SELECT matched_faq_id FROM system_responses WHERE id = ?"
	dc.db.QueryRow(query, responseID).Scan(&faqID)

	if faqID == nil {
		return
	}

	// 更新反馈统计
	var updateQuery string
	var args []interface{}

	switch feedbackType {
	case "helpful":
		updateQuery = `
			UPDATE faq_performance 
			SET positive_feedback = positive_feedback + 1,
				avg_rating = CASE 
					WHEN ? IS NOT NULL THEN (avg_rating * (positive_feedback + negative_feedback - 1) + ?) / (positive_feedback + negative_feedback)
					ELSE avg_rating
				END,
				updated_at = NOW()
			WHERE faq_id = ?
		`
		args = []interface{}{rating, getIntValue(rating), *faqID}
	case "not_helpful":
		updateQuery = `
			UPDATE faq_performance 
			SET negative_feedback = negative_feedback + 1,
				avg_rating = CASE 
					WHEN ? IS NOT NULL THEN (avg_rating * (positive_feedback + negative_feedback - 1) + ?) / (positive_feedback + negative_feedback)
					ELSE avg_rating
				END,
				updated_at = NOW()
			WHERE faq_id = ?
		`
		args = []interface{}{rating, getIntValue(rating), *faqID}
	}

	if updateQuery != "" {
		dc.db.Exec(updateQuery, args...)
	}
}

// GetLearningMetrics 获取学习指标
func (dc *DataCollector) GetLearningMetrics() (*LearningMetrics, error) {
	metrics := &LearningMetrics{}

	// 查询总数统计
	queries := []struct {
		query  string
		target *int64
	}{
		{"SELECT COUNT(*) FROM user_queries", &metrics.TotalQueries},
		{"SELECT COUNT(*) FROM system_responses", &metrics.TotalResponses},
		{"SELECT COUNT(*) FROM user_feedback", &metrics.TotalFeedback},
	}

	for _, q := range queries {
		if err := dc.db.QueryRow(q.query).Scan(q.target); err != nil {
			logger.Warnf("Failed to get metric: %v", err)
		}
	}

	// 正面反馈率
	var positiveFeedback int64
	dc.db.QueryRow("SELECT COUNT(*) FROM user_feedback WHERE feedback_type = 'helpful'").Scan(&positiveFeedback)
	if metrics.TotalFeedback > 0 {
		metrics.PositiveFeedbackRate = float32(positiveFeedback) / float32(metrics.TotalFeedback)
	}

	// 平均置信度
	dc.db.QueryRow("SELECT AVG(confidence_score) FROM system_responses WHERE confidence_score IS NOT NULL").Scan(&metrics.AvgConfidenceScore)

	// 平均处理时间
	dc.db.QueryRow("SELECT AVG(processing_time_ms) FROM system_responses").Scan(&metrics.AvgProcessingTime)

	// 学习模式数量
	var patternCount int
	dc.db.QueryRow("SELECT COUNT(*) FROM learning_patterns").Scan(&patternCount)
	metrics.LearningPatterns = patternCount

	// 表现最好的FAQ
	rows, err := dc.db.Query(`
		SELECT faq_id FROM faq_performance 
		ORDER BY (positive_feedback - negative_feedback) DESC, avg_rating DESC 
		LIMIT 10
	`)
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var faqID int
			if rows.Scan(&faqID) == nil {
				metrics.TopPerformingFAQs = append(metrics.TopPerformingFAQs, faqID)
			}
		}
	}

	return metrics, nil
}

// 辅助函数
func getFloat32Value(f *float32) float32 {
	if f == nil {
		return 0.0
	}
	return *f
}

func getIntValue(i *int) int {
	if i == nil {
		return 0
	}
	return *i
}
