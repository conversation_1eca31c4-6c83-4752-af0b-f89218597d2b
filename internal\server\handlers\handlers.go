package handlers

import (
	"net/http"
	"time"

	"faq-system/internal/health"
	"faq-system/internal/logger"
	"faq-system/internal/rag"

	"github.com/gin-gonic/gin"
)

// Handlers HTTP处理器
type Handlers struct {
	ragSystem     *rag.ChatSystem
	healthChecker *health.Checker
	stats         *Stats
}

// Stats 统计信息
type Stats struct {
	TotalQuestions int64     `json:"total_questions"`
	StartTime      time.Time `json:"start_time"`
	LastQuestion   string    `json:"last_question,omitempty"`
	LastAnswer     string    `json:"last_answer,omitempty"`
}

// New 创建新的处理器
func New(ragSystem *rag.ChatSystem, healthChecker *health.Checker) *Handlers {
	return &Handlers{
		ragSystem:     ragSystem,
		healthChecker: healthChecker,
		stats: &Stats{
			StartTime: time.Now(),
		},
	}
}

// HealthCheck 健康检查处理器
func (h *Handlers) HealthCheck(c *gin.Context) {
	status := h.healthChecker.Check()

	if status.Status == "healthy" {
		c.JSON(http.StatusOK, status)
	} else {
		c.JSON(http.StatusServiceUnavailable, status)
	}
}

// AskQuestion 问答处理器
func (h *Handlers) AskQuestion(c *gin.Context) {
	start := time.Now()

	var req struct {
		Question string `json:"question" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request: " + err.Error(),
		})
		return
	}

	// 记录请求
	logger.Infof("Processing question: %s", req.Question)
	h.stats.TotalQuestions++
	h.stats.LastQuestion = req.Question

	// 获取用户ID（基于IP）
	userID := "user_" + c.ClientIP()

	// 使用RAG系统处理问题
	response, err := h.ragSystem.ProcessQuery(userID, req.Question)
	if err != nil {
		logger.Errorf("RAG processing failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to process question",
		})
		return
	}

	// 记录响应
	h.stats.LastAnswer = response.Answer
	logger.Infof("RAG response: intent=%s, confidence=%.2f, duration=%v",
		response.Intent, response.Confidence, response.Duration)

	// 返回响应
	c.JSON(http.StatusOK, gin.H{
		"answer":     response.Answer,
		"source":     response.Source,
		"intent":     response.Intent,
		"confidence": response.Confidence,
		"duration":   time.Since(start).String(),
	})
}

// GetStats 获取统计信息
func (h *Handlers) GetStats(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"stats":  h.stats,
		"uptime": time.Since(h.stats.StartTime).String(),
	})
}
