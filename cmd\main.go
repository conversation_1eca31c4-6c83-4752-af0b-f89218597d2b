package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"faq-system/internal/app"
)

func main() {
	// 创建应用程序实例
	application, err := app.New()
	if err != nil {
		log.Fatalf("Failed to create application: %v", err)
	}

	// 设置优雅关闭
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan

		log.Println("Shutting down gracefully...")
		if err := application.Shutdown(); err != nil {
			log.Printf("Error during shutdown: %v", err)
		}
		os.Exit(0)
	}()

	// 启动应用程序
	if err := application.Run(); err != nil {
		log.Fatalf("Failed to run application: %v", err)
	}
}
