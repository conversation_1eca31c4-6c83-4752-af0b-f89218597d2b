package mysql

import (
	"database/sql"
	"faq-system/internal/config"
	"faq-system/internal/logger"
	"fmt"

	_ "github.com/go-sql-driver/mysql"
)

// FAQ FAQ数据结构
type FAQ struct {
	ID       int    `json:"id"`
	Question string `json:"question"`
	Answer   string `json:"answer"`
}

// LoadFAQs 从MySQL加载FAQ数据
func LoadFAQs(cfg *config.Config) ([]FAQ, error) {
	// 使用原来的方法构建DSN
	dbDSN := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local&collation=utf8mb4_unicode_ci",
		cfg.MySQL.Username,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
		cfg.MySQL.Database,
	)

	db, err := sql.Open("mysql", dbDSN)
	if err != nil {
		return nil, err
	}
	defer db.Close()

	rows, err := db.Query("SELECT id, question, answer FROM faq")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var faqs []FAQ
	for rows.Next() {
		var faq FAQ
		if err := rows.Scan(&faq.ID, &faq.Question, &faq.Answer); err != nil {
			logger.Warnf("Failed to scan FAQ row: %v", err)
			continue
		}
		faqs = append(faqs, faq)
	}

	return faqs, nil
}

// TestConnection 测试MySQL连接
func TestConnection(cfg *config.Config) error {
	// 使用原来的方法构建DSN
	dbDSN := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local&collation=utf8mb4_unicode_ci",
		cfg.MySQL.Username,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
		cfg.MySQL.Database,
	)

	db, err := sql.Open("mysql", dbDSN)
	if err != nil {
		return err
	}
	defer db.Close()

	return db.Ping()
}

// Connect 连接到MySQL数据库并返回连接
func Connect(cfg *config.Config) (*sql.DB, error) {
	dbDSN := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local&collation=utf8mb4_unicode_ci",
		cfg.MySQL.Username,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
		cfg.MySQL.Database)

	db, err := sql.Open("mysql", dbDSN)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// 设置连接字符集
	_, err = db.Exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci")
	if err != nil {
		return nil, fmt.Errorf("failed to set charset: %w", err)
	}

	_, err = db.Exec("SET CHARACTER SET utf8mb4")
	if err != nil {
		return nil, fmt.Errorf("failed to set character set: %w", err)
	}

	return db, nil
}

// LoadFAQsFromDB 从数据库连接加载FAQ数据
func LoadFAQsFromDB(db *sql.DB) ([]FAQ, error) {
	rows, err := db.Query("SELECT id, question, answer FROM faq")
	if err != nil {
		return nil, fmt.Errorf("failed to query FAQs: %w", err)
	}
	defer rows.Close()

	var faqs []FAQ
	for rows.Next() {
		var faq FAQ
		if err := rows.Scan(&faq.ID, &faq.Question, &faq.Answer); err != nil {
			logger.Warnf("Failed to scan FAQ row: %v", err)
			continue
		}
		faqs = append(faqs, faq)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating FAQ rows: %w", err)
	}

	return faqs, nil
}
