package math

import (
	"fmt"
	"math"
	"regexp"
	"strconv"
	"strings"
)

// Calculator 数学计算器
type Calculator struct {
	variables map[string]float64
}

// NewCalculator 创建计算器
func NewCalculator() *Calculator {
	return &Calculator{
		variables: make(map[string]float64),
	}
}

// Calculate 计算数学表达式
func (c *Calculator) Calculate(expression string) (float64, error) {
	// 预处理表达式
	expr := c.preprocessExpression(expression)
	
	// 解析并计算
	result, err := c.evaluateExpression(expr)
	if err != nil {
		return 0, fmt.Errorf("计算错误: %v", err)
	}
	
	return result, nil
}

// CalculateWithSteps 计算并返回步骤
func (c *Calculator) CalculateWithSteps(expression string) (float64, []string, error) {
	steps := []string{}
	steps = append(steps, fmt.Sprintf("原始表达式: %s", expression))
	
	// 预处理
	expr := c.preprocessExpression(expression)
	if expr != expression {
		steps = append(steps, fmt.Sprintf("预处理后: %s", expr))
	}
	
	// 计算
	result, err := c.evaluateExpression(expr)
	if err != nil {
		return 0, steps, err
	}
	
	steps = append(steps, fmt.Sprintf("计算结果: %g", result))
	
	return result, steps, nil
}

// preprocessExpression 预处理表达式
func (c *Calculator) preprocessExpression(expr string) string {
	// 移除空格
	expr = strings.ReplaceAll(expr, " ", "")
	
	// 替换中文运算符
	expr = strings.ReplaceAll(expr, "×", "*")
	expr = strings.ReplaceAll(expr, "÷", "/")
	expr = strings.ReplaceAll(expr, "（", "(")
	expr = strings.ReplaceAll(expr, "）", ")")
	
	// 处理隐式乘法 (如 2x -> 2*x, 3(4) -> 3*(4))
	expr = c.addImplicitMultiplication(expr)
	
	// 处理函数名
	expr = c.processFunctions(expr)
	
	return expr
}

// addImplicitMultiplication 添加隐式乘法
func (c *Calculator) addImplicitMultiplication(expr string) string {
	// 数字后跟字母或左括号
	re1 := regexp.MustCompile(`(\d)([a-zA-Z\(])`)
	expr = re1.ReplaceAllString(expr, "$1*$2")
	
	// 右括号后跟数字或字母或左括号
	re2 := regexp.MustCompile(`(\))([a-zA-Z\d\(])`)
	expr = re2.ReplaceAllString(expr, "$1*$2")
	
	return expr
}

// processFunctions 处理函数
func (c *Calculator) processFunctions(expr string) string {
	// 支持的函数列表
	functions := []string{"sin", "cos", "tan", "log", "ln", "sqrt", "abs", "exp"}
	
	for _, fn := range functions {
		// 查找函数调用
		pattern := fmt.Sprintf(`%s\(([^)]+)\)`, fn)
		re := regexp.MustCompile(pattern)
		
		for re.MatchString(expr) {
			matches := re.FindStringSubmatch(expr)
			if len(matches) >= 2 {
				arg := matches[1]
				argValue, err := c.evaluateExpression(arg)
				if err != nil {
					continue
				}
				
				result := c.callFunction(fn, argValue)
				expr = strings.Replace(expr, matches[0], fmt.Sprintf("%g", result), 1)
			}
		}
	}
	
	return expr
}

// callFunction 调用数学函数
func (c *Calculator) callFunction(name string, arg float64) float64 {
	switch name {
	case "sin":
		return math.Sin(arg)
	case "cos":
		return math.Cos(arg)
	case "tan":
		return math.Tan(arg)
	case "log":
		return math.Log10(arg)
	case "ln":
		return math.Log(arg)
	case "sqrt":
		return math.Sqrt(arg)
	case "abs":
		return math.Abs(arg)
	case "exp":
		return math.Exp(arg)
	default:
		return 0
	}
}

// evaluateExpression 计算表达式
func (c *Calculator) evaluateExpression(expr string) (float64, error) {
	// 移除空格
	expr = strings.ReplaceAll(expr, " ", "")
	
	if expr == "" {
		return 0, fmt.Errorf("空表达式")
	}
	
	// 处理括号
	for strings.Contains(expr, "(") {
		// 找到最内层括号
		start := -1
		for i, char := range expr {
			if char == '(' {
				start = i
			} else if char == ')' && start != -1 {
				// 计算括号内的表达式
				inner := expr[start+1 : i]
				result, err := c.evaluateExpression(inner)
				if err != nil {
					return 0, err
				}
				
				// 替换括号表达式
				expr = expr[:start] + fmt.Sprintf("%g", result) + expr[i+1:]
				break
			}
		}
		
		if start != -1 && !strings.Contains(expr, ")") {
			return 0, fmt.Errorf("括号不匹配")
		}
	}
	
	// 计算加减法
	return c.evaluateAddSub(expr)
}

// evaluateAddSub 计算加减法
func (c *Calculator) evaluateAddSub(expr string) (float64, error) {
	// 分割加减法
	parts := []string{}
	operators := []string{}
	current := ""
	
	for i, char := range expr {
		if (char == '+' || char == '-') && i > 0 {
			parts = append(parts, current)
			operators = append(operators, string(char))
			current = ""
		} else {
			current += string(char)
		}
	}
	parts = append(parts, current)
	
	if len(parts) == 1 {
		return c.evaluateMulDiv(parts[0])
	}
	
	// 计算第一部分
	result, err := c.evaluateMulDiv(parts[0])
	if err != nil {
		return 0, err
	}
	
	// 依次计算其他部分
	for i, op := range operators {
		value, err := c.evaluateMulDiv(parts[i+1])
		if err != nil {
			return 0, err
		}
		
		if op == "+" {
			result += value
		} else {
			result -= value
		}
	}
	
	return result, nil
}

// evaluateMulDiv 计算乘除法
func (c *Calculator) evaluateMulDiv(expr string) (float64, error) {
	// 分割乘除法
	parts := []string{}
	operators := []string{}
	current := ""
	
	for _, char := range expr {
		if char == '*' || char == '/' {
			parts = append(parts, current)
			operators = append(operators, string(char))
			current = ""
		} else {
			current += string(char)
		}
	}
	parts = append(parts, current)
	
	if len(parts) == 1 {
		return c.evaluateNumber(parts[0])
	}
	
	// 计算第一部分
	result, err := c.evaluateNumber(parts[0])
	if err != nil {
		return 0, err
	}
	
	// 依次计算其他部分
	for i, op := range operators {
		value, err := c.evaluateNumber(parts[i+1])
		if err != nil {
			return 0, err
		}
		
		if op == "*" {
			result *= value
		} else {
			if value == 0 {
				return 0, fmt.Errorf("除零错误")
			}
			result /= value
		}
	}
	
	return result, nil
}

// evaluateNumber 计算数字或变量
func (c *Calculator) evaluateNumber(expr string) (float64, error) {
	expr = strings.TrimSpace(expr)
	
	// 处理负号
	if strings.HasPrefix(expr, "-") {
		value, err := c.evaluateNumber(expr[1:])
		if err != nil {
			return 0, err
		}
		return -value, nil
	}
	
	// 处理正号
	if strings.HasPrefix(expr, "+") {
		return c.evaluateNumber(expr[1:])
	}
	
	// 尝试解析为数字
	if value, err := strconv.ParseFloat(expr, 64); err == nil {
		return value, nil
	}
	
	// 检查是否为变量
	if value, exists := c.variables[expr]; exists {
		return value, nil
	}
	
	// 处理常数
	switch expr {
	case "pi", "π":
		return math.Pi, nil
	case "e":
		return math.E, nil
	}
	
	return 0, fmt.Errorf("无法识别的表达式: %s", expr)
}

// SetVariable 设置变量
func (c *Calculator) SetVariable(name string, value float64) {
	c.variables[name] = value
}

// GetVariable 获取变量
func (c *Calculator) GetVariable(name string) (float64, bool) {
	value, exists := c.variables[name]
	return value, exists
}

// SolveLinearEquation 求解一元一次方程 ax + b = 0
func (c *Calculator) SolveLinearEquation(a, b float64) (float64, error) {
	if a == 0 {
		if b == 0 {
			return 0, fmt.Errorf("方程有无穷多解")
		}
		return 0, fmt.Errorf("方程无解")
	}
	return -b / a, nil
}

// SolveQuadraticEquation 求解一元二次方程 ax² + bx + c = 0
func (c *Calculator) SolveQuadraticEquation(a, b, c0 float64) ([]float64, error) {
	if a == 0 {
		// 退化为一次方程
		solution, err := c.SolveLinearEquation(b, c0)
		if err != nil {
			return nil, err
		}
		return []float64{solution}, nil
	}
	
	discriminant := b*b - 4*a*c0
	
	if discriminant < 0 {
		return nil, fmt.Errorf("方程无实数解")
	}
	
	if discriminant == 0 {
		solution := -b / (2 * a)
		return []float64{solution}, nil
	}
	
	sqrtD := math.Sqrt(discriminant)
	x1 := (-b + sqrtD) / (2 * a)
	x2 := (-b - sqrtD) / (2 * a)
	
	return []float64{x1, x2}, nil
}


// FormatResult 格式化计算结果
func (c *Calculator) FormatResult(result float64) string {
	// 如果是整数，显示为整数
	if result == math.Trunc(result) {
		return fmt.Sprintf("%.0f", result)
	}
	
	// 保留适当的小数位数
	if math.Abs(result) >= 1000000 || (math.Abs(result) < 0.001 && result != 0) {
		return fmt.Sprintf("%.3e", result)
	}
	
	return fmt.Sprintf("%.6g", result)
}
