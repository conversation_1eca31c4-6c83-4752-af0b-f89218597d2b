package health

import (
	"faq-system/internal/config"
	"faq-system/internal/mysql"
	"faq-system/internal/vectorstore"
	"time"
)

// Checker 健康检查器 - 与原系统保持一致的结构
type Checker struct {
	config      *config.Config
	vectorStore *vectorstore.VectorStore
}

// Status 健康状态 - 与原系统保持一致的结构
type Status struct {
	Status    string            `json:"status"`
	Timestamp time.Time         `json:"timestamp"`
	Services  map[string]string `json:"services"`
}

// NewChecker 创建健康检查器
func NewChecker(cfg *config.Config, vs *vectorstore.VectorStore) *Checker {
	return &Checker{
		config:      cfg,
		vectorStore: vs,
	}
}

// Check 执行健康检查 - 与原系统保持一致的逻辑
func (hc *Checker) Check() *Status {
	status := &Status{
		Timestamp: time.Now(),
		Services:  make(map[string]string),
	}

	// 检查MySQL连接
	if err := mysql.TestConnection(hc.config); err != nil {
		status.Services["mysql"] = "unhealthy: " + err.Error()
	} else {
		status.Services["mysql"] = "healthy"
	}

	// 检查向量存储
	if err := hc.vectorStore.TestConnection(); err != nil {
		status.Services["vectorstore"] = "unhealthy: " + err.Error()
	} else {
		status.Services["vectorstore"] = "healthy"
	}

	// 检查LocalAI（可选）
	if hc.config.LocalAI.AutoInstall {
		// 这里可以添加LocalAI健康检查
		status.Services["localai"] = "enabled"
	} else {
		status.Services["localai"] = "disabled"
	}

	// 确定整体状态 - 与原系统保持一致
	allHealthy := true
	for _, serviceStatus := range status.Services {
		if serviceStatus != "healthy" && serviceStatus != "enabled" && serviceStatus != "disabled" {
			allHealthy = false
			break
		}
	}

	if allHealthy {
		status.Status = "healthy"
	} else {
		status.Status = "unhealthy"
	}

	return status
}
