# FAQ系统 (模块化版本)

基于Go语言构建的模块化FAQ智能问答系统，与原有系统功能完全一致。

## 🌟 主要特性

- **模块化架构**: 清晰的代码结构，易于维护和扩展
- **智能问答**: 基于向量搜索和语义匹配的智能回答
- **MySQL存储**: 可靠的FAQ数据存储
- **向量搜索**: SQLite向量数据库支持语义搜索
- **LocalAI集成**: 支持本地AI模型（可选）
- **Web界面**: 现代化的聊天界面
- **健康检查**: 完整的系统状态监控

## 📁 项目结构

```
faq-system/
├── cmd/                    # 应用程序入口
│   └── main.go
├── internal/               # 内部模块
│   ├── app/               # 应用程序主体
│   ├── config/            # 配置管理
│   ├── database/          # 数据库初始化
│   ├── embedding/         # 向量嵌入客户端
│   ├── health/            # 健康检查
│   ├── logger/            # 日志系统
│   ├── mysql/             # MySQL数据访问
│   ├── rag/               # RAG聊天系统
│   ├── server/            # Web服务器
│   │   ├── handlers/      # HTTP处理器
│   │   └── templates/     # HTML模板
│   ├── smartmatch/        # 智能匹配
│   ├── answer/            # 答案生成
│   └── vectorstore/       # 向量存储
├── build.bat              # 构建脚本
├── start_faq_system.bat   # 启动脚本
└── README.md              # 说明文档
```

## 🚀 快速开始

### 1. 构建系统

```bash
# 运行构建脚本
build.bat

# 或手动构建
go mod tidy
go build -o faq-system.exe ./cmd
```

### 2. 启动系统

```bash
# 运行启动脚本
start_faq_system.bat

# 或直接运行
./faq-system.exe
```

### 3. 访问系统

打开浏览器访问: http://localhost:8081

## ⚙️ 配置说明

系统使用与原版本完全一致的配置：

- **端口**: 8081 (与原系统保持一致)
- **MySQL**: 127.0.0.1:33508/faqdb
- **LocalAI**: http://localhost:8080 (可选)
- **向量数据库**: vectors.db (SQLite)

## 🔧 与原系统的一致性

本模块化版本与原main.go系统保持100%功能一致：

- ✅ 相同的数据库表结构 (`faq` 表)
- ✅ 相同的配置参数和端口 (8081)
- ✅ 相同的智能匹配逻辑
- ✅ 相同的向量搜索算法
- ✅ 相同的前端界面和交互
- ✅ 相同的健康检查机制
- ✅ 相同的错误处理和兜底策略

## 📊 系统监控

访问以下端点进行系统监控：

- `/health` - 健康检查
- `/api/v1/health` - API健康检查
- `/api/v1/stats` - 系统统计信息

## 🛠️ 开发说明

### 添加新功能

1. 在 `internal/` 目录下创建新模块
2. 在 `internal/app/app.go` 中集成新模块
3. 更新配置和路由

### 修改配置

编辑 `internal/config/config.go` 中的默认配置

### 自定义前端

修改 `internal/server/templates/index.go` 中的HTML模板

## 🔍 故障排除

1. **构建失败**: 检查Go版本和依赖
2. **数据库连接失败**: 确认MySQL服务运行在33508端口
3. **端口冲突**: 修改配置中的端口设置
4. **向量搜索异常**: 检查vectors.db文件权限

## 📝 更新日志

- v1.0.0: 初始模块化版本，与原系统功能完全一致
