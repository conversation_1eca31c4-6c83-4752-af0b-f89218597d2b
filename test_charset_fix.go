package main

import (
	"database/sql"
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/database"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	fmt.Println("🔧 测试字符集修复...")

	// 加载配置
	cfg := config.Load()

	// 删除并重新创建数据库
	fmt.Println("🗑️ 删除旧数据库...")
	serverDSN := fmt.Sprintf("%s:%s@tcp(%s:%s)/?charset=utf8mb4&parseTime=True&loc=Local&collation=utf8mb4_general_ci",
		cfg.MySQL.Username,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
	)

	db, err := sql.Open("mysql", serverDSN)
	if err != nil {
		log.Fatalf("连接MySQL服务器失败: %v", err)
	}
	defer db.Close()

	// 删除数据库
	_, err = db.Exec("DROP DATABASE IF EXISTS " + cfg.MySQL.Database)
	if err != nil {
		log.Fatalf("删除数据库失败: %v", err)
	}
	fmt.Println("✅ 旧数据库已删除")

	// 重新初始化数据库
	fmt.Println("🔄 重新初始化数据库...")
	if err := database.Initialize(cfg); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}
	fmt.Println("✅ 数据库初始化成功")

	// 连接到新数据库
	newDB, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("连接新数据库失败: %v", err)
	}
	defer newDB.Close()

	// 检查表的字符集
	fmt.Println("🔍 检查表的字符集...")
	query := `
		SELECT TABLE_NAME, TABLE_COLLATION 
		FROM information_schema.TABLES 
		WHERE TABLE_SCHEMA = ? 
		ORDER BY TABLE_NAME
	`

	rows, err := newDB.Query(query, cfg.MySQL.Database)
	if err != nil {
		log.Fatalf("查询表信息失败: %v", err)
	}
	defer rows.Close()

	fmt.Println("表名\t\t\t排序规则")
	fmt.Println("---\t\t\t---")

	for rows.Next() {
		var tableName, collation string
		err := rows.Scan(&tableName, &collation)
		if err != nil {
			continue
		}

		status := "✅"
		if collation != "utf8mb4_general_ci" {
			status = "❌"
		}

		fmt.Printf("%s %s\t\t%s\n", status, tableName, collation)
	}

	// 插入测试数据
	fmt.Println("\n📝 插入测试数据...")
	testData := []string{
		`INSERT INTO learned_knowledge 
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES 
		('什么是C#？', 'C#是微软开发的一种面向对象的编程语言。', 'user_input', 0.9, 'technology', '["c#", "编程语言"]', '测试', 'system', 'approved')`,

		`INSERT INTO learned_knowledge 
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES 
		('什么是Python？', 'Python是一种高级编程语言。', 'user_input', 0.9, 'technology', '["python", "编程语言"]', '测试', 'system', 'approved')`,
	}

	for i, sql := range testData {
		_, err := newDB.Exec(sql)
		if err != nil {
			fmt.Printf("❌ 插入测试数据 %d 失败: %v\n", i+1, err)
		} else {
			fmt.Printf("✅ 插入测试数据 %d 成功\n", i+1)
		}
	}

	// 测试搜索功能
	fmt.Println("\n🔍 测试搜索功能...")
	testQueries := []string{"C#", "Python", "编程"}

	for _, testQuery := range testQueries {
		fmt.Printf("\n查询: %s\n", testQuery)

		searchQuery := `
			SELECT id, question, answer, confidence 
			FROM learned_knowledge
			WHERE status IN ('approved', 'pending')
			AND (question COLLATE utf8mb4_general_ci LIKE CONCAT('%', ? COLLATE utf8mb4_general_ci, '%')
			     OR answer COLLATE utf8mb4_general_ci LIKE CONCAT('%', ? COLLATE utf8mb4_general_ci, '%'))
			ORDER BY confidence DESC
			LIMIT 3
		`

		searchRows, err := newDB.Query(searchQuery, testQuery, testQuery)
		if err != nil {
			fmt.Printf("  ❌ 搜索失败: %v\n", err)
			continue
		}

		found := false
		for searchRows.Next() {
			var id int
			var question, answer string
			var confidence float64

			err := searchRows.Scan(&id, &question, &answer, &confidence)
			if err != nil {
				continue
			}

			found = true
			fmt.Printf("  ✅ 找到: %s (置信度: %.2f)\n", question, confidence)
		}
		searchRows.Close()

		if !found {
			fmt.Printf("  ❌ 未找到匹配结果\n")
		}
	}

	fmt.Println("\n🎉 字符集修复测试完成！")
}
