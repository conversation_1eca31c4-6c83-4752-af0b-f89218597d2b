package learning

import (
	"database/sql"
	"encoding/json"
	"strings"

	"faq-system/internal/logger"
)

// SmartMatcher 智能匹配器 - 应用学习到的规则
type SmartMatcher struct {
	db    *sql.DB
	rules []SmartMatchingRule
}

// SmartMatchingRule 智能匹配规则
type SmartMatchingRule struct {
	ID                 int                    `json:"id"`
	RuleName           string                 `json:"rule_name"`
	RuleType           string                 `json:"rule_type"`
	Conditions         map[string]interface{} `json:"conditions"`
	Actions            map[string]interface{} `json:"actions"`
	Priority           int                    `json:"priority"`
	EffectivenessScore float32                `json:"effectiveness_score"`
	UsageCount         int                    `json:"usage_count"`
	SuccessRate        float32                `json:"success_rate"`
	IsActive           bool                   `json:"is_active"`
}

// MatchingContext 匹配上下文
type MatchingContext struct {
	Query         string                 `json:"query"`
	UserID        string                 `json:"user_id"`
	Intent        string                 `json:"intent"`
	UserSegment   string                 `json:"user_segment"`
	ThinkingStyle string                 `json:"thinking_style"`
	SessionLength int                    `json:"session_length"`
	PreviousQuery string                 `json:"previous_query"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// MatchingResult 匹配结果
type MatchingResult struct {
	OriginalScore   float32                `json:"original_score"`
	AdjustedScore   float32                `json:"adjusted_score"`
	AppliedRules    []string               `json:"applied_rules"`
	ResponseStyle   string                 `json:"response_style"`
	DetailLevel     string                 `json:"detail_level"`
	IncludeExamples bool                   `json:"include_examples"`
	ContextWeight   float32                `json:"context_weight"`
	Adjustments     map[string]interface{} `json:"adjustments"`
}

// NewSmartMatcher 创建智能匹配器
func NewSmartMatcher(db *sql.DB) *SmartMatcher {
	matcher := &SmartMatcher{
		db: db,
	}
	matcher.loadRules()
	return matcher
}

// loadRules 加载匹配规则
func (sm *SmartMatcher) loadRules() error {
	query := `
		SELECT id, rule_name, rule_type, conditions, actions, priority, 
		       effectiveness_score, usage_count, success_rate, is_active
		FROM smart_matching_rules 
		WHERE is_active = TRUE
		ORDER BY priority DESC, effectiveness_score DESC
	`

	rows, err := sm.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	sm.rules = nil // 清空现有规则
	for rows.Next() {
		var rule SmartMatchingRule
		var conditionsJSON, actionsJSON string

		err := rows.Scan(&rule.ID, &rule.RuleName, &rule.RuleType, &conditionsJSON, &actionsJSON,
			&rule.Priority, &rule.EffectivenessScore, &rule.UsageCount, &rule.SuccessRate, &rule.IsActive)
		if err != nil {
			continue
		}

		// 解析JSON
		json.Unmarshal([]byte(conditionsJSON), &rule.Conditions)
		json.Unmarshal([]byte(actionsJSON), &rule.Actions)

		sm.rules = append(sm.rules, rule)
	}

	logger.Infof("🧠 加载了 %d 条智能匹配规则", len(sm.rules))
	return nil
}

// ApplySmartMatching 应用智能匹配
func (sm *SmartMatcher) ApplySmartMatching(context *MatchingContext, originalScore float32) *MatchingResult {
	result := &MatchingResult{
		OriginalScore: originalScore,
		AdjustedScore: originalScore,
		AppliedRules:  []string{},
		ResponseStyle: "standard",
		DetailLevel:   "medium",
		ContextWeight: 0.0,
		Adjustments:   make(map[string]interface{}),
	}

	// 按优先级应用规则
	for _, rule := range sm.rules {
		if sm.shouldApplyRule(rule, context) {
			sm.applyRule(rule, context, result)
			result.AppliedRules = append(result.AppliedRules, rule.RuleName)

			// 更新规则使用统计
			sm.updateRuleUsage(rule.ID)
		}
	}

	// 确保调整后的分数在合理范围内
	if result.AdjustedScore > 1.0 {
		result.AdjustedScore = 1.0
	} else if result.AdjustedScore < 0.0 {
		result.AdjustedScore = 0.0
	}

	logger.Infof("🎯 智能匹配: %.3f -> %.3f (应用了 %d 条规则)",
		originalScore, result.AdjustedScore, len(result.AppliedRules))

	return result
}

// shouldApplyRule 判断是否应该应用规则
func (sm *SmartMatcher) shouldApplyRule(rule SmartMatchingRule, context *MatchingContext) bool {
	switch rule.RuleType {
	case "keyword_boost":
		return sm.checkKeywordConditions(rule.Conditions, context)
	case "intent_mapping":
		return sm.checkIntentConditions(rule.Conditions, context)
	case "user_preference":
		return sm.checkUserPreferenceConditions(rule.Conditions, context)
	case "context_aware":
		return sm.checkContextConditions(rule.Conditions, context)
	default:
		return false
	}
}

// checkKeywordConditions 检查关键词条件
func (sm *SmartMatcher) checkKeywordConditions(conditions map[string]interface{}, context *MatchingContext) bool {
	keywords, ok := conditions["keywords"].([]interface{})
	if !ok {
		return false
	}

	query := strings.ToLower(context.Query)
	for _, keyword := range keywords {
		if keywordStr, ok := keyword.(string); ok {
			if strings.Contains(query, strings.ToLower(keywordStr)) {
				return true
			}
		}
	}
	return false
}

// checkIntentConditions 检查意图条件
func (sm *SmartMatcher) checkIntentConditions(conditions map[string]interface{}, context *MatchingContext) bool {
	requiredIntent, ok := conditions["intent"].(string)
	if !ok {
		return false
	}

	if context.Intent != requiredIntent {
		return false
	}

	// 检查置信度阈值
	if threshold, ok := conditions["confidence_threshold"].(float64); ok {
		// 这里需要从上下文获取当前置信度，暂时返回true
		_ = threshold
		return true
	}

	return true
}

// checkUserPreferenceConditions 检查用户偏好条件
func (sm *SmartMatcher) checkUserPreferenceConditions(conditions map[string]interface{}, context *MatchingContext) bool {
	if userSegment, ok := conditions["user_segment"].(string); ok {
		if context.UserSegment != userSegment {
			return false
		}
	}

	if thinkingStyle, ok := conditions["thinking_style"].(string); ok {
		if context.ThinkingStyle != thinkingStyle {
			return false
		}
	}

	return true
}

// checkContextConditions 检查上下文条件
func (sm *SmartMatcher) checkContextConditions(conditions map[string]interface{}, context *MatchingContext) bool {
	if needsPrevious, ok := conditions["previous_queries"].(bool); ok && needsPrevious {
		if context.PreviousQuery == "" {
			return false
		}
	}

	if sessionLengthCondition, ok := conditions["session_length"].(string); ok {
		if sessionLengthCondition == ">1" && context.SessionLength <= 1 {
			return false
		}
	}

	return true
}

// applyRule 应用规则
func (sm *SmartMatcher) applyRule(rule SmartMatchingRule, context *MatchingContext, result *MatchingResult) {
	actions := rule.Actions["action"]
	parameters, _ := rule.Actions["parameters"].(map[string]interface{})

	switch actions {
	case "boost_confidence":
		if factor, ok := parameters["factor"].(float64); ok {
			result.AdjustedScore *= float32(factor)
			result.Adjustments["boost_factor"] = factor
		}

	case "prefer_detailed_answer":
		if level, ok := parameters["detail_level"].(string); ok {
			result.DetailLevel = level
		}

	case "adjust_response_style":
		if style, ok := parameters["style"].(string); ok {
			result.ResponseStyle = style
		}
		if includeExamples, ok := parameters["include_examples"].(bool); ok {
			result.IncludeExamples = includeExamples
		}

	case "consider_context":
		if weight, ok := parameters["context_weight"].(float64); ok {
			result.ContextWeight = float32(weight)
		}
	}

	logger.Infof("📋 应用规则: %s (%s)", rule.RuleName, actions)
}

// updateRuleUsage 更新规则使用统计
func (sm *SmartMatcher) updateRuleUsage(ruleID int) {
	query := `
		UPDATE smart_matching_rules 
		SET usage_count = usage_count + 1,
		    last_updated = NOW()
		WHERE id = ?
	`
	sm.db.Exec(query, ruleID)
}

// GetUserThinkingStyle 获取用户思维风格
func (sm *SmartMatcher) GetUserThinkingStyle(userID string) (string, string) {
	// 从用户思维模式表中查询用户的思维风格
	query := `
		SELECT user_segment, thinking_style 
		FROM user_thinking_patterns 
		WHERE pattern_name LIKE ? 
		ORDER BY success_rate DESC, usage_count DESC 
		LIMIT 1
	`

	var userSegment, thinkingStyle string
	err := sm.db.QueryRow(query, "%"+userID+"%").Scan(&userSegment, &thinkingStyle)
	if err != nil {
		// 如果没有找到，返回默认值
		return "general", "direct"
	}

	return userSegment, thinkingStyle
}

// RefreshRules 刷新规则
func (sm *SmartMatcher) RefreshRules() error {
	return sm.loadRules()
}
