package main

import (
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 检查FAQ数据库中的数据...")

	// 查询所有FAQ数据
	rows, err := db.Query("SELECT id, question, answer FROM faq ORDER BY id")
	if err != nil {
		log.Fatalf("Failed to query FAQ data: %v", err)
	}
	defer rows.Close()

	fmt.Println("\n📋 数据库中的FAQ数据：")
	fmt.Println("==================================================================================")

	count := 0
	for rows.Next() {
		var id int
		var question, answer string

		if err := rows.Scan(&id, &question, &answer); err != nil {
			log.Printf("Failed to scan row: %v", err)
			continue
		}

		count++
		fmt.Printf("\n🆔 ID: %d\n", id)
		fmt.Printf("❓ 问题: %s\n", question)
		fmt.Printf("💡 答案: %s\n", answer[:min(100, len(answer))])
		if len(answer) > 100 {
			fmt.Printf("   ... (答案被截断，总长度: %d 字符)\n", len(answer))
		}
		fmt.Println("--------------------------------------------------------------------------------")
	}

	fmt.Printf("\n📊 总共找到 %d 条FAQ数据\n", count)

	// 特别检查LocalAI相关的问题
	fmt.Println("\n🔍 检查LocalAI相关问题...")
	rows2, err := db.Query("SELECT id, question, answer FROM faq WHERE question LIKE '%LocalAI%' OR question LIKE '%localai%'")
	if err != nil {
		log.Printf("Failed to query LocalAI data: %v", err)
		return
	}
	defer rows2.Close()

	localaiCount := 0
	for rows2.Next() {
		var id int
		var question, answer string

		if err := rows2.Scan(&id, &question, &answer); err != nil {
			log.Printf("Failed to scan LocalAI row: %v", err)
			continue
		}

		localaiCount++
		fmt.Printf("\n🎯 找到LocalAI相关问题:\n")
		fmt.Printf("🆔 ID: %d\n", id)
		fmt.Printf("❓ 问题: %s\n", question)
		fmt.Printf("💡 答案: %s\n", answer)
	}

	if localaiCount == 0 {
		fmt.Println("❌ 没有找到包含'LocalAI'的问题")
	} else {
		fmt.Printf("✅ 找到 %d 个LocalAI相关问题\n", localaiCount)
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
