@echo off
echo ========================================
echo 构建FAQ系统 (模块化版本)
echo ========================================
echo.

echo 清理依赖...
go mod tidy

echo.
echo 构建可执行文件...
go build -o faq-system.exe ./cmd

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo 构建成功！
    echo 可执行文件: faq-system.exe
    echo 启动命令: start_faq_system.bat
    echo ========================================
) else (
    echo.
    echo ========================================
    echo 构建失败！请检查错误信息
    echo ========================================
)

pause
