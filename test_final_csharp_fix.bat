@echo off
echo 🧪 测试C#问题最终修复...
echo.

echo 💻 1. 测试"c#是什么"...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"c#是什么\"}"
echo.
echo.

echo 💻 2. 测试"C#是什么"...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"C#是什么\"}"
echo.
echo.

echo 💻 3. 测试"什么是C#"...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是C#\"}"
echo.
echo.

echo 💻 4. 测试"C#是什么语言"...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"C#是什么语言\"}"
echo.
echo.

echo 💻 5. 测试单独的"C#"...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"C#\"}"
echo.
echo.

echo 🐍 6. 测试Python问题（对比）...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"python是什么\"}"
echo.
echo.

echo 🐍 7. 测试"什么是Python"...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是Python\"}"
echo.
echo.

echo 📱 8. 测试手机问题（对比）...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"手机是什么\"}"
echo.
echo.

echo 🎉 测试完成！
echo.
echo 预期结果：
echo - C#相关问题应该返回详细的C#介绍，而不是"C#是什么语言。"
echo - Python问题应该返回详细的Python介绍
echo - 手机问题应该返回详细的手机介绍
echo.
pause
