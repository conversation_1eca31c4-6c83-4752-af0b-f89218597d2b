package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
)

func main() {
	fmt.Println("🔍 调试'手机是什么'问题...")

	// 测试问题
	testQuestions := []string{
		"手机是什么",
		"什么是手机",
		"手机",
		"什么是Python",
		"Python是什么",
	}

	for _, question := range testQuestions {
		fmt.Printf("\n" + "="*50 + "\n")
		fmt.Printf("🤔 测试问题: %s\n", question)
		fmt.Printf("="*50 + "\n")

		// 发送请求到FAQ系统
		response, err := sendQuestion(question)
		if err != nil {
			fmt.Printf("❌ 请求失败: %v\n", err)
			continue
		}

		// 解析响应
		var result map[string]interface{}
		if err := json.Unmarshal(response, &result); err != nil {
			fmt.Printf("❌ 解析响应失败: %v\n", err)
			fmt.Printf("原始响应: %s\n", string(response))
			continue
		}

		// 显示结果
		answer, ok := result["answer"].(string)
		if !ok {
			fmt.Printf("❌ 无法获取答案\n")
			continue
		}

		source, _ := result["source"].(string)
		confidence, _ := result["confidence"].(float64)
		intent, _ := result["intent"].(string)

		fmt.Printf("📝 答案: %s\n", answer)
		fmt.Printf("📊 来源: %s\n", source)
		fmt.Printf("🎯 意图: %s\n", intent)
		fmt.Printf("💯 置信度: %.2f\n", confidence)

		// 检查是否是问题重复的情况
		if strings.TrimSpace(answer) == question {
			fmt.Printf("🚨 发现问题：答案就是问题本身！\n")
		} else if strings.Contains(answer, question) && len(answer) < len(question)*2 {
			fmt.Printf("⚠️ 可能的问题：答案包含问题且过于简短\n")
		} else {
			fmt.Printf("✅ 答案看起来正常\n")
		}
	}

	fmt.Println("\n🎉 调试完成！")
}

func sendQuestion(question string) ([]byte, error) {
	url := "http://localhost:8081/ask"
	
	payload := fmt.Sprintf(`{"question": "%s"}`, question)
	
	resp, err := http.Post(url, "application/json", strings.NewReader(payload))
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	body := make([]byte, 4096)
	n, err := resp.Body.Read(body)
	if err != nil && err.Error() != "EOF" {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	return body[:n], nil
}
