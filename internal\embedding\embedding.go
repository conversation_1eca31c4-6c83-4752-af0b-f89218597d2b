package embedding

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

// Client embedding客户端 - 与原系统保持一致的结构
type Client struct {
	BaseURL string
	Model   string
}

// embedRequest embedding请求 - 与原系统保持一致
type embedRequest struct {
	Model string   `json:"model"`
	Input []string `json:"input"`
}

// embedResponse embedding响应 - 与原系统保持一致
type embedResponse struct {
	Data []struct {
		Embedding []float32 `json:"embedding"`
	} `json:"data"`
}

// NewClient 创建embedding客户端 - 与原系统保持一致的接口
func NewClient(baseURL, model string) *Client {
	return &Client{
		BaseURL: baseURL,
		Model:   model,
	}
}

// EmbedText 调用 LocalAI embedding 接口，返回向量 - 与原系统保持一致
func (c *Client) EmbedText(text string) ([]float32, error) {
	reqBody := embedRequest{
		Model: c.Model,
		Input: []string{text},
	}
	data, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}

	resp, err := http.Post(fmt.Sprintf("%s/v1/embeddings", c.BaseURL), "application/json", bytes.NewReader(data))
	if err != nil {
		// 如果LocalAI不可用，返回模拟向量
		return c.generateMockEmbedding(text), nil
	}
	defer resp.Body.Close()

	var res embedResponse
	if err := json.NewDecoder(resp.Body).Decode(&res); err != nil {
		// 如果解析失败，返回模拟向量
		return c.generateMockEmbedding(text), nil
	}
	if len(res.Data) == 0 {
		return c.generateMockEmbedding(text), nil
	}

	return res.Data[0].Embedding, nil
}

// generateMockEmbedding 生成模拟向量（用于测试） - 与原系统保持一致
func (c *Client) generateMockEmbedding(text string) []float32 {
	// 基于文本内容生成简单的模拟向量
	hash := 0
	for _, char := range text {
		hash = hash*31 + int(char)
	}

	// 生成384维向量（常见的embedding维度）
	embedding := make([]float32, 384)
	for i := range embedding {
		// 使用简单的伪随机生成
		hash = (hash*1103515245 + 12345) & 0x7fffffff
		embedding[i] = float32(hash%1000-500) / 1000.0 // 范围 [-0.5, 0.5]
	}

	// 归一化向量
	var norm float32
	for _, val := range embedding {
		norm += val * val
	}
	norm = float32(1.0 / (1e-8 + float64(norm))) // 避免除零

	for i := range embedding {
		embedding[i] *= norm
	}

	return embedding
}

// TestConnection 测试embedding服务连接
func (c *Client) TestConnection() error {
	resp, err := http.Get(c.BaseURL + "/v1/models")
	if err != nil {
		return fmt.Errorf("无法连接到embedding服务: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("embedding服务返回错误状态: %d", resp.StatusCode)
	}

	return nil
}
