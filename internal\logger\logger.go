package logger

import (
	"log"
	"os"
)

// Logger 日志记录器 - 与原系统保持一致的结构
type Logger struct {
	info  *log.Logger
	warn  *log.Logger
	error *log.Logger
}

// New 创建新的日志记录器 - 与原系统保持一致
func New() *Logger {
	return &Logger{
		info:  log.New(os.Stdout, "INFO: ", log.Ldate|log.Ltime|log.Lshortfile),
		warn:  log.New(os.Stdout, "WARN: ", log.Ldate|log.Ltime|log.Lshortfile),
		error: log.New(os.Stderr, "ERROR: ", log.Ldate|log.Ltime|log.Lshortfile),
	}
}

// 全局日志实例
var globalLogger *Logger

// Init 初始化全局日志系统
func Init() {
	globalLogger = New()
}

// Info 记录信息日志
func Info(v ...interface{}) {
	if globalLogger != nil {
		globalLogger.Info(v...)
	}
}

// Infof 记录格式化信息日志
func Infof(format string, v ...interface{}) {
	if globalLogger != nil {
		globalLogger.Infof(format, v...)
	}
}

// Warn 记录警告日志
func Warn(v ...interface{}) {
	if globalLogger != nil {
		globalLogger.Warn(v...)
	}
}

// Warnf 记录格式化警告日志
func Warnf(format string, v ...interface{}) {
	if globalLogger != nil {
		globalLogger.Warnf(format, v...)
	}
}

// Error 记录错误日志
func Error(v ...interface{}) {
	if globalLogger != nil {
		globalLogger.Error(v...)
	}
}

// Errorf 记录格式化错误日志
func Errorf(format string, v ...interface{}) {
	if globalLogger != nil {
		globalLogger.Errorf(format, v...)
	}
}

// Logger 实例方法
func (l *Logger) Info(v ...interface{}) {
	l.info.Println(v...)
}

func (l *Logger) Infof(format string, v ...interface{}) {
	l.info.Printf(format, v...)
}

func (l *Logger) Warn(v ...interface{}) {
	l.warn.Println(v...)
}

func (l *Logger) Warnf(format string, v ...interface{}) {
	l.warn.Printf(format, v...)
}

func (l *Logger) Error(v ...interface{}) {
	l.error.Println(v...)
}

func (l *Logger) Errorf(format string, v ...interface{}) {
	l.error.Printf(format, v...)
}

func (l *Logger) Fatal(v ...interface{}) {
	l.error.Println(v...)
	os.Exit(1)
}

func (l *Logger) Fatalf(format string, v ...interface{}) {
	l.error.Printf(format, v...)
	os.Exit(1)
}
