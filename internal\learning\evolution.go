package learning

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"time"

	"faq-system/internal/logger"
)

// EvolutionEngine 智能进化引擎 - 真正的学习和进化
type EvolutionEngine struct {
	db     *sql.DB
	config *LearningConfig
}

// UserThinkingPattern 用户思维模式
type UserThinkingPattern struct {
	ID              int                    `json:"id"`
	PatternType     string                 `json:"pattern_type"` // query_style, intent_preference, feedback_pattern
	PatternName     string                 `json:"pattern_name"`
	UserSegment     string                 `json:"user_segment"`     // technical, business, general
	ThinkingStyle   string                 `json:"thinking_style"`   // direct, exploratory, comparative
	QueryPatterns   []string               `json:"query_patterns"`   // 用户常用的查询模式
	PreferredTopics []string               `json:"preferred_topics"` // 偏好的话题
	ResponseStyle   string                 `json:"response_style"`   // detailed, concise, example_based
	Confidence      float32                `json:"confidence"`
	UsageCount      int                    `json:"usage_count"`
	SuccessRate     float32                `json:"success_rate"`
	LastUpdated     time.Time              `json:"last_updated"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// SystemEvolution 系统进化记录
type SystemEvolution struct {
	ID              int       `json:"id"`
	EvolutionType   string    `json:"evolution_type"` // matching_improvement, response_optimization, new_pattern_discovery
	Description     string    `json:"description"`
	BeforeMetrics   string    `json:"before_metrics"`   // JSON格式的改进前指标
	AfterMetrics    string    `json:"after_metrics"`    // JSON格式的改进后指标
	ImprovementRate float32   `json:"improvement_rate"` // 改进幅度
	AppliedAt       time.Time `json:"applied_at"`
	Status          string    `json:"status"` // active, testing, deprecated
}

// NewEvolutionEngine 创建进化引擎
func NewEvolutionEngine(db *sql.DB, config *LearningConfig) *EvolutionEngine {
	return &EvolutionEngine{
		db:     db,
		config: config,
	}
}

// AnalyzeUserThinking 分析用户思维模式
func (ee *EvolutionEngine) AnalyzeUserThinking() error {
	logger.Info("🧠 开始分析用户思维模式...")

	// 1. 分析查询风格模式
	if err := ee.analyzeQueryStyles(); err != nil {
		logger.Errorf("查询风格分析失败: %v", err)
	}

	// 2. 分析意图偏好模式
	if err := ee.analyzeIntentPreferences(); err != nil {
		logger.Errorf("意图偏好分析失败: %v", err)
	}

	// 3. 分析反馈模式
	if err := ee.analyzeFeedbackPatterns(); err != nil {
		logger.Errorf("反馈模式分析失败: %v", err)
	}

	// 4. 发现新的思维模式
	if err := ee.discoverNewPatterns(); err != nil {
		logger.Errorf("新模式发现失败: %v", err)
	}

	logger.Info("✅ 用户思维模式分析完成")
	return nil
}

// analyzeQueryStyles 分析查询风格
func (ee *EvolutionEngine) analyzeQueryStyles() error {
	// 分析用户查询的语言特征
	query := `
		SELECT 
			uq.user_id,
			uq.query_text,
			uq.query_intent,
			sr.confidence_score,
			COALESCE(uf.feedback_type, 'none') as feedback,
			COALESCE(uf.rating, 0) as rating
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		LEFT JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
		ORDER BY uq.user_id, uq.created_at
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	userQueries := make(map[string][]QueryAnalysis)
	for rows.Next() {
		var qa QueryAnalysis
		err := rows.Scan(&qa.UserID, &qa.QueryText, &qa.Intent, &qa.Confidence, &qa.Feedback, &qa.Rating)
		if err != nil {
			continue
		}
		userQueries[qa.UserID] = append(userQueries[qa.UserID], qa)
	}

	// 为每个用户分析查询风格
	for userID, queries := range userQueries {
		if len(queries) < 3 { // 至少需要3个查询才能分析模式
			continue
		}

		pattern := ee.extractQueryStylePattern(userID, queries)
		if pattern != nil {
			ee.saveUserThinkingPattern(pattern)
		}
	}

	return nil
}

// extractQueryStylePattern 提取查询风格模式
func (ee *EvolutionEngine) extractQueryStylePattern(userID string, queries []QueryAnalysis) *UserThinkingPattern {
	// 分析查询长度分布
	var totalLength, shortQueries, longQueries int
	var questionWords, directQueries int
	var technicalTerms int

	for _, q := range queries {
		length := len(strings.Fields(q.QueryText))
		totalLength += length

		if length <= 3 {
			shortQueries++
		} else if length >= 8 {
			longQueries++
		}

		// 检查是否包含疑问词
		if strings.Contains(q.QueryText, "什么") || strings.Contains(q.QueryText, "如何") ||
			strings.Contains(q.QueryText, "怎么") || strings.Contains(q.QueryText, "为什么") {
			questionWords++
		}

		// 检查是否是直接查询（没有疑问词）
		if !strings.Contains(q.QueryText, "什么") && !strings.Contains(q.QueryText, "如何") {
			directQueries++
		}

		// 检查技术术语
		technicalKeywords := []string{"API", "数据库", "算法", "向量", "MySQL", "Go", "LocalAI"}
		for _, keyword := range technicalKeywords {
			if strings.Contains(q.QueryText, keyword) {
				technicalTerms++
				break
			}
		}
	}

	avgLength := float32(totalLength) / float32(len(queries))

	// 确定思维风格
	var thinkingStyle string
	if float32(questionWords)/float32(len(queries)) > 0.7 {
		thinkingStyle = "exploratory" // 探索型
	} else if float32(directQueries)/float32(len(queries)) > 0.6 {
		thinkingStyle = "direct" // 直接型
	} else {
		thinkingStyle = "comparative" // 比较型
	}

	// 确定用户细分
	var userSegment string
	if float32(technicalTerms)/float32(len(queries)) > 0.5 {
		userSegment = "technical"
	} else {
		userSegment = "general"
	}

	// 确定偏好的响应风格
	var responseStyle string
	if avgLength > 6 {
		responseStyle = "detailed" // 详细型用户偏好详细回答
	} else {
		responseStyle = "concise" // 简洁型用户偏好简洁回答
	}

	// 计算成功率
	var successfulQueries int
	for _, q := range queries {
		if q.Confidence > 0.5 || q.Feedback == "helpful" || q.Rating >= 4 {
			successfulQueries++
		}
	}
	successRate := float32(successfulQueries) / float32(len(queries))

	return &UserThinkingPattern{
		PatternType:   "query_style",
		PatternName:   fmt.Sprintf("%s_%s_style", userSegment, thinkingStyle),
		UserSegment:   userSegment,
		ThinkingStyle: thinkingStyle,
		ResponseStyle: responseStyle,
		Confidence:    0.8,
		UsageCount:    len(queries),
		SuccessRate:   successRate,
		LastUpdated:   time.Now(),
		Metadata: map[string]interface{}{
			"avg_query_length": avgLength,
			"question_ratio":   float32(questionWords) / float32(len(queries)),
			"direct_ratio":     float32(directQueries) / float32(len(queries)),
			"technical_ratio":  float32(technicalTerms) / float32(len(queries)),
		},
	}
}

// analyzeIntentPreferences 分析意图偏好
func (ee *EvolutionEngine) analyzeIntentPreferences() error {
	// 分析不同意图的成功率和用户满意度
	query := `
		SELECT 
			uq.query_intent,
			sr.matched_faq_id,
			AVG(sr.confidence_score) as avg_confidence,
			COUNT(*) as query_count,
			SUM(CASE WHEN uf.feedback_type = 'helpful' THEN 1 ELSE 0 END) as helpful_count,
			AVG(COALESCE(uf.rating, 0)) as avg_rating
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		LEFT JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
		  AND uq.query_intent IS NOT NULL
		GROUP BY uq.query_intent, sr.matched_faq_id
		HAVING query_count >= 3
		ORDER BY avg_confidence DESC, avg_rating DESC
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	intentPatterns := make(map[string]*UserThinkingPattern)
	for rows.Next() {
		var intent string
		var faqID sql.NullInt64
		var avgConfidence float32
		var queryCount, helpfulCount int
		var avgRating float32

		err := rows.Scan(&intent, &faqID, &avgConfidence, &queryCount, &helpfulCount, &avgRating)
		if err != nil {
			continue
		}

		successRate := float32(helpfulCount) / float32(queryCount)

		// 如果这个意图模式表现良好，记录为用户偏好
		if avgConfidence > 0.6 && successRate > 0.7 {
			pattern := &UserThinkingPattern{
				PatternType:   "intent_preference",
				PatternName:   fmt.Sprintf("preferred_%s", intent),
				UserSegment:   "general",
				ThinkingStyle: intent,
				Confidence:    avgConfidence,
				UsageCount:    queryCount,
				SuccessRate:   successRate,
				LastUpdated:   time.Now(),
				Metadata: map[string]interface{}{
					"avg_confidence": avgConfidence,
					"avg_rating":     avgRating,
					"helpful_ratio":  successRate,
				},
			}
			intentPatterns[intent] = pattern
		}
	}

	// 保存意图偏好模式
	for _, pattern := range intentPatterns {
		ee.saveUserThinkingPattern(pattern)
	}

	return nil
}

// analyzeFeedbackPatterns 分析反馈模式
func (ee *EvolutionEngine) analyzeFeedbackPatterns() error {
	// 分析用户反馈的模式，了解用户对不同类型回答的偏好
	query := `
		SELECT 
			sr.response_source,
			sr.confidence_score,
			uf.feedback_type,
			uf.rating,
			uf.feedback_text,
			COUNT(*) as feedback_count
		FROM system_responses sr
		JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE sr.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
		GROUP BY sr.response_source, sr.confidence_score, uf.feedback_type, uf.rating
		HAVING feedback_count >= 2
		ORDER BY feedback_count DESC
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	feedbackPatterns := make(map[string]*UserThinkingPattern)
	for rows.Next() {
		var source, feedbackType string
		var confidence float32
		var rating, feedbackCount int
		var feedbackText sql.NullString

		err := rows.Scan(&source, &confidence, &feedbackType, &rating, &feedbackText, &feedbackCount)
		if err != nil {
			continue
		}

		// 分析正面反馈的模式
		if feedbackType == "helpful" && rating >= 4 {
			patternName := fmt.Sprintf("positive_%s_feedback", strings.ReplaceAll(source, " ", "_"))

			pattern := &UserThinkingPattern{
				PatternType:   "feedback_pattern",
				PatternName:   patternName,
				UserSegment:   "general",
				ThinkingStyle: "positive_feedback",
				ResponseStyle: source,
				Confidence:    confidence,
				UsageCount:    feedbackCount,
				SuccessRate:   1.0, // 正面反馈的成功率为100%
				LastUpdated:   time.Now(),
				Metadata: map[string]interface{}{
					"source":         source,
					"avg_confidence": confidence,
					"avg_rating":     rating,
					"feedback_type":  feedbackType,
				},
			}
			feedbackPatterns[patternName] = pattern
		}
	}

	// 保存反馈模式
	for _, pattern := range feedbackPatterns {
		ee.saveUserThinkingPattern(pattern)
	}

	return nil
}

// QueryAnalysis 查询分析结构
type QueryAnalysis struct {
	UserID     string
	QueryText  string
	Intent     string
	Confidence float32
	Feedback   string
	Rating     int
}

// discoverNewPatterns 发现新的思维模式
func (ee *EvolutionEngine) discoverNewPatterns() error {
	logger.Info("🔍 发现新的用户思维模式...")

	// 使用聚类分析发现新的查询模式
	query := `
		SELECT
			uq.query_text,
			uq.query_intent,
			sr.confidence_score,
			sr.response_source,
			COALESCE(uf.feedback_type, 'none') as feedback
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		LEFT JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
		  AND sr.confidence_score < 0.5  -- 关注低置信度的查询，可能是新模式
		ORDER BY uq.created_at DESC
		LIMIT 100
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	var lowConfidenceQueries []QueryAnalysis
	for rows.Next() {
		var qa QueryAnalysis
		var source string
		err := rows.Scan(&qa.QueryText, &qa.Intent, &qa.Confidence, &source, &qa.Feedback)
		if err != nil {
			continue
		}
		lowConfidenceQueries = append(lowConfidenceQueries, qa)
	}

	// 分析这些低置信度查询的共同特征
	if len(lowConfidenceQueries) >= 5 {
		newPattern := ee.analyzeEmergingPattern(lowConfidenceQueries)
		if newPattern != nil {
			ee.saveUserThinkingPattern(newPattern)
			logger.Infof("🆕 发现新的思维模式: %s", newPattern.PatternName)
		}
	}

	return nil
}

// analyzeEmergingPattern 分析新兴模式
func (ee *EvolutionEngine) analyzeEmergingPattern(queries []QueryAnalysis) *UserThinkingPattern {
	// 分析查询的共同特征
	commonKeywords := ee.findCommonKeywords(queries)
	if len(commonKeywords) == 0 {
		return nil
	}

	// 计算平均置信度
	var totalConfidence float32
	for _, q := range queries {
		totalConfidence += q.Confidence
	}
	avgConfidence := totalConfidence / float32(len(queries))

	// 如果发现了有意义的模式
	if len(commonKeywords) >= 2 && avgConfidence < 0.3 {
		return &UserThinkingPattern{
			PatternType:   "emerging_pattern",
			PatternName:   fmt.Sprintf("emerging_%s", strings.Join(commonKeywords[:2], "_")),
			UserSegment:   "general",
			ThinkingStyle: "emerging",
			QueryPatterns: commonKeywords,
			Confidence:    0.6, // 新模式的初始置信度
			UsageCount:    len(queries),
			SuccessRate:   avgConfidence, // 使用置信度作为成功率的初始值
			LastUpdated:   time.Now(),
			Metadata: map[string]interface{}{
				"avg_confidence":   avgConfidence,
				"pattern_keywords": commonKeywords,
				"discovery_date":   time.Now().Format("2006-01-02"),
			},
		}
	}

	return nil
}

// findCommonKeywords 查找共同关键词
func (ee *EvolutionEngine) findCommonKeywords(queries []QueryAnalysis) []string {
	wordCount := make(map[string]int)

	for _, q := range queries {
		words := strings.Fields(strings.ToLower(q.QueryText))
		for _, word := range words {
			// 过滤掉常见的停用词
			if !ee.isStopWord(word) && len(word) > 1 {
				wordCount[word]++
			}
		}
	}

	// 找出出现频率高的词
	var commonWords []string
	threshold := len(queries) / 3 // 至少在1/3的查询中出现
	for word, count := range wordCount {
		if count >= threshold {
			commonWords = append(commonWords, word)
		}
	}

	return commonWords
}

// isStopWord 判断是否为停用词
func (ee *EvolutionEngine) isStopWord(word string) bool {
	stopWords := []string{"的", "是", "在", "有", "和", "与", "或", "但", "如果", "那么", "这个", "那个", "什么", "如何", "怎么", "为什么"}
	for _, stopWord := range stopWords {
		if word == stopWord {
			return true
		}
	}
	return false
}

// saveUserThinkingPattern 保存用户思维模式
func (ee *EvolutionEngine) saveUserThinkingPattern(pattern *UserThinkingPattern) error {
	// 检查是否已存在相似模式
	existingID, err := ee.findSimilarPattern(pattern)
	if err == nil && existingID > 0 {
		// 更新现有模式
		return ee.updateThinkingPattern(existingID, pattern)
	}

	// 创建新模式
	metadataJSON, _ := json.Marshal(pattern.Metadata)
	queryPatternsJSON, _ := json.Marshal(pattern.QueryPatterns)
	preferredTopicsJSON, _ := json.Marshal(pattern.PreferredTopics)

	query := `
		INSERT INTO user_thinking_patterns
		(pattern_type, pattern_name, user_segment, thinking_style, query_patterns,
		 preferred_topics, response_style, confidence, usage_count, success_rate, metadata)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err = ee.db.Exec(query,
		pattern.PatternType, pattern.PatternName, pattern.UserSegment, pattern.ThinkingStyle,
		queryPatternsJSON, preferredTopicsJSON, pattern.ResponseStyle,
		pattern.Confidence, pattern.UsageCount, pattern.SuccessRate, metadataJSON)

	if err != nil {
		return fmt.Errorf("保存思维模式失败: %v", err)
	}

	logger.Infof("💾 保存新的思维模式: %s (成功率: %.2f)", pattern.PatternName, pattern.SuccessRate)
	return nil
}

// findSimilarPattern 查找相似模式
func (ee *EvolutionEngine) findSimilarPattern(pattern *UserThinkingPattern) (int, error) {
	query := `
		SELECT id FROM user_thinking_patterns
		WHERE pattern_type = ? AND user_segment = ? AND thinking_style = ?
		LIMIT 1
	`

	var id int
	err := ee.db.QueryRow(query, pattern.PatternType, pattern.UserSegment, pattern.ThinkingStyle).Scan(&id)
	if err == sql.ErrNoRows {
		return 0, nil
	}
	return id, err
}

// updateThinkingPattern 更新思维模式
func (ee *EvolutionEngine) updateThinkingPattern(id int, pattern *UserThinkingPattern) error {
	metadataJSON, _ := json.Marshal(pattern.Metadata)

	query := `
		UPDATE user_thinking_patterns
		SET usage_count = usage_count + ?,
		    success_rate = (success_rate + ?) / 2,
		    confidence = (confidence + ?) / 2,
		    metadata = ?,
		    last_updated = NOW()
		WHERE id = ?
	`

	_, err := ee.db.Exec(query, pattern.UsageCount, pattern.SuccessRate, pattern.Confidence, metadataJSON, id)
	if err != nil {
		return fmt.Errorf("更新思维模式失败: %v", err)
	}

	logger.Infof("🔄 更新思维模式 ID=%d", id)
	return nil
}

// ApplyEvolutionaryImprovements 应用进化改进
func (ee *EvolutionEngine) ApplyEvolutionaryImprovements() error {
	logger.Info("🚀 开始应用进化改进...")

	// 1. 基于用户思维模式优化匹配算法
	if err := ee.optimizeMatchingAlgorithm(); err != nil {
		logger.Errorf("匹配算法优化失败: %v", err)
	}

	// 2. 基于反馈模式优化响应生成
	if err := ee.optimizeResponseGeneration(); err != nil {
		logger.Errorf("响应生成优化失败: %v", err)
	}

	// 3. 动态调整系统参数
	if err := ee.adaptSystemParameters(); err != nil {
		logger.Errorf("系统参数调整失败: %v", err)
	}

	// 4. 记录进化历史
	if err := ee.recordEvolutionHistory(); err != nil {
		logger.Errorf("进化历史记录失败: %v", err)
	}

	logger.Info("✅ 进化改进应用完成")
	return nil
}

// optimizeMatchingAlgorithm 优化匹配算法
func (ee *EvolutionEngine) optimizeMatchingAlgorithm() error {
	// 获取表现最好的思维模式
	patterns, err := ee.getBestPerformingPatterns()
	if err != nil {
		return err
	}

	for _, pattern := range patterns {
		// 根据成功的模式调整匹配权重
		if pattern.SuccessRate > 0.8 {
			err := ee.adjustMatchingWeights(pattern)
			if err != nil {
				logger.Warnf("调整匹配权重失败: %v", err)
			}
		}
	}

	return nil
}

// getBestPerformingPatterns 获取表现最好的模式
func (ee *EvolutionEngine) getBestPerformingPatterns() ([]*UserThinkingPattern, error) {
	query := `
		SELECT pattern_type, pattern_name, user_segment, thinking_style,
		       response_style, confidence, usage_count, success_rate, metadata
		FROM user_thinking_patterns
		WHERE success_rate > 0.7 AND usage_count >= 5
		ORDER BY success_rate DESC, usage_count DESC
		LIMIT 10
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var patterns []*UserThinkingPattern
	for rows.Next() {
		pattern := &UserThinkingPattern{}
		var metadataJSON string

		err := rows.Scan(&pattern.PatternType, &pattern.PatternName, &pattern.UserSegment,
			&pattern.ThinkingStyle, &pattern.ResponseStyle, &pattern.Confidence,
			&pattern.UsageCount, &pattern.SuccessRate, &metadataJSON)
		if err != nil {
			continue
		}

		json.Unmarshal([]byte(metadataJSON), &pattern.Metadata)
		patterns = append(patterns, pattern)
	}

	return patterns, nil
}

// adjustMatchingWeights 调整匹配权重
func (ee *EvolutionEngine) adjustMatchingWeights(pattern *UserThinkingPattern) error {
	// 根据用户思维模式调整FAQ匹配的权重
	var adjustmentFactor float32 = 1.0 + (pattern.SuccessRate-0.5)*0.5 // 成功率越高，权重调整越大

	// 更新学习配置中的权重参数
	configUpdates := map[string]interface{}{
		fmt.Sprintf("weight_%s", pattern.ThinkingStyle): adjustmentFactor,
		fmt.Sprintf("boost_%s", pattern.UserSegment):    pattern.SuccessRate,
	}

	for key, value := range configUpdates {
		err := ee.updateLearningConfig(key, value)
		if err != nil {
			logger.Warnf("更新配置失败 %s: %v", key, err)
		}
	}

	logger.Infof("🎯 调整匹配权重: %s -> %.3f", pattern.PatternName, adjustmentFactor)
	return nil
}

// optimizeResponseGeneration 优化响应生成
func (ee *EvolutionEngine) optimizeResponseGeneration() error {
	// 分析用户偏好的响应风格
	stylePreferences, err := ee.analyzeResponseStylePreferences()
	if err != nil {
		return err
	}

	// 根据偏好调整响应生成策略
	for style, preference := range stylePreferences {
		if preference > 0.7 {
			err := ee.updateResponseStrategy(style, preference)
			if err != nil {
				logger.Warnf("更新响应策略失败 %s: %v", style, err)
			}
		}
	}

	return nil
}

// analyzeResponseStylePreferences 分析响应风格偏好
func (ee *EvolutionEngine) analyzeResponseStylePreferences() (map[string]float32, error) {
	query := `
		SELECT
			utp.response_style,
			AVG(utp.success_rate) as avg_success_rate,
			COUNT(*) as pattern_count
		FROM user_thinking_patterns utp
		WHERE utp.pattern_type = 'feedback_pattern'
		  AND utp.success_rate > 0.5
		GROUP BY utp.response_style
		HAVING pattern_count >= 2
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	preferences := make(map[string]float32)
	for rows.Next() {
		var style string
		var avgSuccessRate float32
		var count int

		err := rows.Scan(&style, &avgSuccessRate, &count)
		if err != nil {
			continue
		}

		preferences[style] = avgSuccessRate
	}

	return preferences, nil
}

// updateResponseStrategy 更新响应策略
func (ee *EvolutionEngine) updateResponseStrategy(style string, preference float32) error {
	// 根据用户偏好更新响应生成的策略配置
	configKey := fmt.Sprintf("response_style_%s_weight", strings.ReplaceAll(style, " ", "_"))
	return ee.updateLearningConfig(configKey, preference)
}

// adaptSystemParameters 动态调整系统参数
func (ee *EvolutionEngine) adaptSystemParameters() error {
	// 基于系统表现动态调整关键参数

	// 1. 调整置信度阈值
	optimalThreshold, err := ee.calculateOptimalConfidenceThreshold()
	if err == nil {
		ee.updateLearningConfig("min_confidence_threshold", optimalThreshold)
		logger.Infof("🎛️ 调整置信度阈值: %.3f", optimalThreshold)
	}

	// 2. 调整反馈权重
	optimalFeedbackWeight, err := ee.calculateOptimalFeedbackWeight()
	if err == nil {
		ee.updateLearningConfig("feedback_weight", optimalFeedbackWeight)
		logger.Infof("🎛️ 调整反馈权重: %.3f", optimalFeedbackWeight)
	}

	return nil
}

// calculateOptimalConfidenceThreshold 计算最优置信度阈值
func (ee *EvolutionEngine) calculateOptimalConfidenceThreshold() (float32, error) {
	// 分析不同置信度阈值下的系统表现
	query := `
		SELECT
			CASE
				WHEN sr.confidence_score >= 0.8 THEN '0.8+'
				WHEN sr.confidence_score >= 0.6 THEN '0.6-0.8'
				WHEN sr.confidence_score >= 0.4 THEN '0.4-0.6'
				WHEN sr.confidence_score >= 0.2 THEN '0.2-0.4'
				ELSE '0.0-0.2'
			END as confidence_range,
			COUNT(*) as total_responses,
			SUM(CASE WHEN uf.feedback_type = 'helpful' THEN 1 ELSE 0 END) as helpful_count,
			AVG(COALESCE(uf.rating, 0)) as avg_rating
		FROM system_responses sr
		LEFT JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE sr.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
		GROUP BY confidence_range
		ORDER BY confidence_range DESC
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return 0, err
	}
	defer rows.Close()

	bestThreshold := float32(0.3) // 默认阈值
	bestScore := float32(0)

	for rows.Next() {
		var confidenceRange string
		var totalResponses, helpfulCount int
		var avgRating float32

		err := rows.Scan(&confidenceRange, &totalResponses, &helpfulCount, &avgRating)
		if err != nil {
			continue
		}

		if totalResponses > 0 {
			helpfulRatio := float32(helpfulCount) / float32(totalResponses)
			// 综合考虑有用率和评分
			score := helpfulRatio*0.7 + (avgRating/5.0)*0.3

			if score > bestScore {
				bestScore = score
				// 根据范围设置阈值
				switch confidenceRange {
				case "0.8+":
					bestThreshold = 0.8
				case "0.6-0.8":
					bestThreshold = 0.6
				case "0.4-0.6":
					bestThreshold = 0.4
				case "0.2-0.4":
					bestThreshold = 0.2
				default:
					bestThreshold = 0.1
				}
			}
		}
	}

	return bestThreshold, nil
}

// calculateOptimalFeedbackWeight 计算最优反馈权重
func (ee *EvolutionEngine) calculateOptimalFeedbackWeight() (float32, error) {
	// 分析反馈权重对系统改进的影响
	query := `
		SELECT
			AVG(CASE WHEN uf.feedback_type = 'helpful' THEN 1.0 ELSE 0.0 END) as helpful_ratio,
			AVG(COALESCE(uf.rating, 0)) as avg_rating,
			COUNT(*) as feedback_count
		FROM user_feedback uf
		WHERE uf.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
	`

	var helpfulRatio, avgRating float32
	var feedbackCount int

	err := ee.db.QueryRow(query).Scan(&helpfulRatio, &avgRating, &feedbackCount)
	if err != nil {
		return 0.7, err // 返回默认值
	}

	// 根据反馈质量计算最优权重
	if feedbackCount > 10 {
		// 反馈质量越高，权重越大
		optimalWeight := helpfulRatio*0.6 + (avgRating/5.0)*0.4
		// 限制在合理范围内
		if optimalWeight < 0.3 {
			optimalWeight = 0.3
		} else if optimalWeight > 0.9 {
			optimalWeight = 0.9
		}
		return optimalWeight, nil
	}

	return 0.7, nil // 默认权重
}

// updateLearningConfig 更新学习配置
func (ee *EvolutionEngine) updateLearningConfig(key string, value interface{}) error {
	// 确定配置值的类型
	var configValue string
	var configType string

	switch v := value.(type) {
	case float32:
		configValue = fmt.Sprintf("%.6f", v)
		configType = "float"
	case int:
		configValue = fmt.Sprintf("%d", v)
		configType = "int"
	case bool:
		configValue = fmt.Sprintf("%t", v)
		configType = "boolean"
	default:
		configValue = fmt.Sprintf("%v", v)
		configType = "string"
	}

	query := `
		INSERT INTO learning_config (config_key, config_value, config_type, description)
		VALUES (?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
		config_value = VALUES(config_value),
		config_type = VALUES(config_type),
		updated_at = NOW()
	`

	description := fmt.Sprintf("Auto-optimized parameter for %s", key)
	_, err := ee.db.Exec(query, key, configValue, configType, description)
	if err != nil {
		return fmt.Errorf("更新学习配置失败: %v", err)
	}

	logger.Infof("⚙️ 更新配置: %s = %s", key, configValue)
	return nil
}

// recordEvolutionHistory 记录进化历史
func (ee *EvolutionEngine) recordEvolutionHistory() error {
	// 获取当前系统指标
	currentMetrics, err := ee.getCurrentSystemMetrics()
	if err != nil {
		return err
	}

	// 获取上次记录的指标
	lastMetrics, err := ee.getLastSystemMetrics()
	if err != nil {
		// 如果没有历史记录，直接保存当前指标
		return ee.saveEvolutionRecord("system_initialization", "系统初始化", "", currentMetrics, 0.0)
	}

	// 计算改进率
	improvementRate := ee.calculateImprovementRate(lastMetrics, currentMetrics)

	// 如果有显著改进，记录进化历史
	if math.Abs(float64(improvementRate)) > 0.05 { // 5%以上的变化才记录
		evolutionType := "performance_optimization"
		description := fmt.Sprintf("系统性能优化，改进率: %.2f%%", improvementRate*100)

		if improvementRate > 0 {
			description = "✅ " + description
		} else {
			description = "⚠️ " + description
		}

		return ee.saveEvolutionRecord(evolutionType, description, lastMetrics, currentMetrics, improvementRate)
	}

	return nil
}

// getCurrentSystemMetrics 获取当前系统指标
func (ee *EvolutionEngine) getCurrentSystemMetrics() (string, error) {
	metrics := make(map[string]interface{})

	// 查询各种系统指标
	queries := map[string]string{
		"avg_confidence": "SELECT AVG(confidence_score) FROM system_responses WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)",
		"avg_rating":     "SELECT AVG(rating) FROM user_feedback WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)",
		"helpful_ratio":  "SELECT AVG(CASE WHEN feedback_type = 'helpful' THEN 1.0 ELSE 0.0 END) FROM user_feedback WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)",
		"response_time":  "SELECT AVG(processing_time_ms) FROM system_responses WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)",
	}

	for key, query := range queries {
		var value sql.NullFloat64
		err := ee.db.QueryRow(query).Scan(&value)
		if err == nil && value.Valid {
			metrics[key] = value.Float64
		} else {
			metrics[key] = 0.0
		}
	}

	// 添加时间戳
	metrics["timestamp"] = time.Now().Format("2006-01-02 15:04:05")

	metricsJSON, err := json.Marshal(metrics)
	return string(metricsJSON), err
}

// getLastSystemMetrics 获取上次系统指标
func (ee *EvolutionEngine) getLastSystemMetrics() (string, error) {
	query := `
		SELECT after_metrics
		FROM system_evolution
		WHERE status = 'active'
		ORDER BY applied_at DESC
		LIMIT 1
	`

	var metrics string
	err := ee.db.QueryRow(query).Scan(&metrics)
	if err == sql.ErrNoRows {
		return "", fmt.Errorf("no previous metrics found")
	}
	return metrics, err
}

// calculateImprovementRate 计算改进率
func (ee *EvolutionEngine) calculateImprovementRate(lastMetrics, currentMetrics string) float32 {
	var last, current map[string]interface{}

	json.Unmarshal([]byte(lastMetrics), &last)
	json.Unmarshal([]byte(currentMetrics), &current)

	// 计算关键指标的综合改进率
	improvements := []float32{}

	// 置信度改进
	if lastConf, ok := last["avg_confidence"].(float64); ok {
		if currentConf, ok := current["avg_confidence"].(float64); ok && lastConf > 0 {
			improvement := float32((currentConf - lastConf) / lastConf)
			improvements = append(improvements, improvement)
		}
	}

	// 用户满意度改进
	if lastRating, ok := last["avg_rating"].(float64); ok {
		if currentRating, ok := current["avg_rating"].(float64); ok && lastRating > 0 {
			improvement := float32((currentRating - lastRating) / lastRating)
			improvements = append(improvements, improvement)
		}
	}

	// 有用率改进
	if lastHelpful, ok := last["helpful_ratio"].(float64); ok {
		if currentHelpful, ok := current["helpful_ratio"].(float64); ok && lastHelpful > 0 {
			improvement := float32((currentHelpful - lastHelpful) / lastHelpful)
			improvements = append(improvements, improvement)
		}
	}

	// 计算平均改进率
	if len(improvements) > 0 {
		var total float32
		for _, imp := range improvements {
			total += imp
		}
		return total / float32(len(improvements))
	}

	return 0.0
}

// saveEvolutionRecord 保存进化记录
func (ee *EvolutionEngine) saveEvolutionRecord(evolutionType, description, beforeMetrics, afterMetrics string, improvementRate float32) error {
	query := `
		INSERT INTO system_evolution (evolution_type, description, before_metrics, after_metrics, improvement_rate, applied_at, status)
		VALUES (?, ?, ?, ?, ?, NOW(), 'active')
	`

	_, err := ee.db.Exec(query, evolutionType, description, beforeMetrics, afterMetrics, improvementRate)
	if err != nil {
		return fmt.Errorf("保存进化记录失败: %v", err)
	}

	logger.Infof("📈 记录系统进化: %s (改进率: %.2f%%)", description, improvementRate*100)
	return nil
}
