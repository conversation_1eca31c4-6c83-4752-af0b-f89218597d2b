package main

import (
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/embedding"
	"faq-system/internal/mysql"
	"faq-system/internal/rag"
	"faq-system/internal/vectorstore"

	_ "github.com/go-sql-driver/mysql"
	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 加载配置
	cfg := config.Load()

	fmt.Println("🔍 直接测试RAG系统...")

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 加载FAQ数据
	faqs, err := mysql.LoadFAQsFromDB(db)
	if err != nil {
		log.Fatalf("Failed to load FAQs: %v", err)
	}
	fmt.Printf("✅ 加载了 %d 条FAQ数据\n", len(faqs))

	// 初始化向量存储
	store, err := vectorstore.NewVectorStore(cfg.VectorStore.Path)
	if err != nil {
		log.Fatalf("Failed to initialize vector store: %v", err)
	}
	defer store.Close()

	// 初始化嵌入客户端
	embedClient := embedding.NewClient(cfg.LocalAI.BaseURL, cfg.LocalAI.EmbedModel)

	// 初始化RAG系统
	ragSystem := rag.NewChatSystem(store, embedClient, faqs)
	fmt.Println("✅ RAG系统初始化完成")

	// 测试查询
	testQueries := []string{
		"什么是LocalAI？",
		"LocalAI是什么？",
		"Go语言有什么优势？",
		"如何使用MySQL存储FAQ？",
	}

	for _, query := range testQueries {
		fmt.Printf("\n🔍 测试查询: %s\n", query)

		// 使用RAG系统处理查询
		response, err := ragSystem.ProcessQuery("test_user", query)
		if err != nil {
			fmt.Printf("❌ 处理失败: %v\n", err)
			continue
		}

		fmt.Printf("✅ 回答: %s\n", response.Answer[:min(100, len(response.Answer))])
		if len(response.Answer) > 100 {
			fmt.Printf("   ... (答案被截断)\n")
		}
		fmt.Printf("📊 来源: %s\n", response.Source)
		fmt.Printf("🎯 意图: %s\n", response.Intent)
		fmt.Printf("📈 置信度: %.2f\n", response.Confidence)
		fmt.Println("---")
	}

	fmt.Println("\n📊 直接测试完成")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
