package crawler

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"faq-system/internal/learning"

	"github.com/PuerkitoBio/goquery"
)

// crawlTarget 爬取目标
func (kc *KnowledgeCrawler) crawlTarget(target *CrawlTarget) (*CrawlResult, error) {
	switch target.Type {
	case "website":
		return kc.crawlWebsite(target)
	case "api":
		return kc.crawlAPI(target)
	case "rss":
		return kc.crawlRSS(target)
	case "search_engine":
		return kc.crawlSearchEngine(target)
	default:
		return kc.crawlWebsite(target)
	}
}

// crawlWebsite 爬取网站
func (kc *KnowledgeCrawler) crawlWebsite(target *CrawlTarget) (*CrawlResult, error) {
	// 发送HTTP请求
	req, err := http.NewRequest("GET", target.URL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", kc.config.UserAgent)

	resp, err := kc.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}

	result := &CrawlResult{
		TargetID:  target.ID,
		URL:       target.URL,
		Category:  target.Category,
		Keywords:  target.Keywords,
		CrawledAt: time.Now(),
		Status:    "pending",
		Metadata:  make(map[string]interface{}),
	}

	// 提取标题
	if titleSelector, ok := target.Selectors["title"]; ok {
		result.Title = strings.TrimSpace(doc.Find(titleSelector).First().Text())
	} else {
		result.Title = strings.TrimSpace(doc.Find("title").First().Text())
	}

	// 提取内容
	if contentSelector, ok := target.Selectors["content"]; ok {
		result.Content = strings.TrimSpace(doc.Find(contentSelector).Text())
	} else {
		// 默认提取主要内容区域
		content := ""
		doc.Find("article, .content, .post, .entry, main, #content").Each(func(i int, s *goquery.Selection) {
			content += s.Text() + "\n"
		})
		result.Content = strings.TrimSpace(content)
	}

	// 如果没有找到内容，尝试提取body中的文本
	if result.Content == "" {
		result.Content = strings.TrimSpace(doc.Find("body").Text())
	}

	// 生成摘要
	result.Summary = kc.generateSummary(result.Content)

	// 提取关键词
	if len(result.Keywords) == 0 {
		result.Keywords = kc.extractKeywords(result.Title + " " + result.Content)
	}

	return result, nil
}

// crawlAPI 爬取API
func (kc *KnowledgeCrawler) crawlAPI(target *CrawlTarget) (*CrawlResult, error) {
	// 简单的API爬取实现
	req, err := http.NewRequest("GET", target.URL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", kc.config.UserAgent)
	req.Header.Set("Accept", "application/json")

	resp, err := kc.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	// 这里可以根据API的具体格式来解析数据
	// 暂时返回一个基本的结果
	result := &CrawlResult{
		TargetID:  target.ID,
		URL:       target.URL,
		Title:     "API数据",
		Content:   "从API获取的数据",
		Category:  target.Category,
		Keywords:  target.Keywords,
		CrawledAt: time.Now(),
		Status:    "pending",
		Metadata:  make(map[string]interface{}),
	}

	return result, nil
}

// crawlRSS 爬取RSS
func (kc *KnowledgeCrawler) crawlRSS(target *CrawlTarget) (*CrawlResult, error) {
	// RSS爬取的简单实现
	result := &CrawlResult{
		TargetID:  target.ID,
		URL:       target.URL,
		Title:     "RSS数据",
		Content:   "从RSS获取的数据",
		Category:  target.Category,
		Keywords:  target.Keywords,
		CrawledAt: time.Now(),
		Status:    "pending",
		Metadata:  make(map[string]interface{}),
	}

	return result, nil
}

// crawlSearchEngine 爬取搜索引擎
func (kc *KnowledgeCrawler) crawlSearchEngine(target *CrawlTarget) (*CrawlResult, error) {
	// 搜索引擎爬取的简单实现
	result := &CrawlResult{
		TargetID:  target.ID,
		URL:       target.URL,
		Title:     "搜索结果",
		Content:   "从搜索引擎获取的数据",
		Category:  target.Category,
		Keywords:  target.Keywords,
		CrawledAt: time.Now(),
		Status:    "pending",
		Metadata:  make(map[string]interface{}),
	}

	return result, nil
}

// generateSummary 生成摘要
func (kc *KnowledgeCrawler) generateSummary(content string) string {
	// 简单的摘要生成：取前200个字符
	content = strings.TrimSpace(content)
	if len(content) <= 200 {
		return content
	}

	// 尝试在句号处截断
	summary := content[:200]
	if lastDot := strings.LastIndex(summary, "。"); lastDot > 100 {
		summary = summary[:lastDot+3] // 包含句号
	}

	return summary + "..."
}

// extractKeywords 提取关键词
func (kc *KnowledgeCrawler) extractKeywords(text string) []string {
	// 简单的关键词提取
	text = strings.ToLower(text)

	// 技术相关关键词
	techKeywords := []string{
		"python", "java", "javascript", "c#", "go", "rust", "php", "ruby",
		"react", "vue", "angular", "node", "express", "django", "spring",
		"mysql", "postgresql", "mongodb", "redis", "elasticsearch",
		"docker", "kubernetes", "aws", "azure", "gcp", "linux", "windows",
		"api", "rest", "graphql", "microservice", "devops", "ci/cd",
		"machine learning", "ai", "deep learning", "tensorflow", "pytorch",
	}

	var keywords []string
	for _, keyword := range techKeywords {
		if strings.Contains(text, keyword) {
			keywords = append(keywords, keyword)
		}
	}

	return keywords
}

// saveCrawlResult 保存爬取结果
func (kc *KnowledgeCrawler) saveCrawlResult(result *CrawlResult) error {
	metadataJSON, _ := json.Marshal(result.Metadata)
	keywordsJSON, _ := json.Marshal(result.Keywords)

	query := `
		INSERT INTO crawl_results 
		(target_id, url, title, content, summary, keywords, category, metadata, crawled_at, status)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := kc.db.Exec(query, result.TargetID, result.URL, result.Title, result.Content,
		result.Summary, keywordsJSON, result.Category, metadataJSON, result.CrawledAt, result.Status)

	return err
}

// updateLastCrawled 更新最后爬取时间
func (kc *KnowledgeCrawler) updateLastCrawled(targetID int) error {
	query := "UPDATE crawl_targets SET last_crawled = NOW() WHERE id = ?"
	_, err := kc.db.Exec(query, targetID)

	// 同时更新内存中的数据
	kc.targetsMutex.Lock()
	if target, exists := kc.targets[targetID]; exists {
		target.LastCrawled = time.Now()
	}
	kc.targetsMutex.Unlock()

	return err
}

// processResults 处理爬取结果
func (kc *KnowledgeCrawler) processResults() {
	// 查询待处理的爬取结果
	query := `
		SELECT id, target_id, url, title, content, summary, keywords, category, metadata, crawled_at
		FROM crawl_results 
		WHERE status = 'pending' 
		ORDER BY crawled_at ASC 
		LIMIT 10
	`

	rows, err := kc.db.Query(query)
	if err != nil {
		log.Printf("查询待处理结果失败: %v", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var result CrawlResult
		var id int
		var keywordsJSON, metadataJSON string

		err := rows.Scan(&id, &result.TargetID, &result.URL, &result.Title, &result.Content,
			&result.Summary, &keywordsJSON, &result.Category, &metadataJSON, &result.CrawledAt)
		if err != nil {
			continue
		}

		json.Unmarshal([]byte(keywordsJSON), &result.Keywords)
		json.Unmarshal([]byte(metadataJSON), &result.Metadata)

		// 处理结果并转换为学习知识
		if err := kc.processResult(id, &result); err != nil {
			log.Printf("处理结果失败 %d: %v", id, err)
		}
	}
}

// processResult 处理单个爬取结果
func (kc *KnowledgeCrawler) processResult(resultID int, result *CrawlResult) error {
	// 从内容中提取知识
	knowledge := kc.extractKnowledgeFromContent(result)
	if knowledge == nil {
		// 标记为已处理但没有提取到知识
		kc.updateResultStatus(resultID, "processed")
		return nil
	}

	// 保存学习知识
	if kc.knowledgeLearner != nil {
		// 暂时使用LearnFromUserInput方法来保存知识
		err := kc.knowledgeLearner.LearnFromUserInput("crawler", knowledge.Question, knowledge.Answer, "")
		if err != nil {
			log.Printf("保存学习知识失败: %v", err)
			kc.updateResultStatus(resultID, "failed")
			return err
		}
	}

	// 标记为已处理
	kc.updateResultStatus(resultID, "processed")
	log.Printf("✅ 处理爬取结果: %s", result.Title)

	return nil
}

// extractKnowledgeFromContent 从内容中提取知识
func (kc *KnowledgeCrawler) extractKnowledgeFromContent(result *CrawlResult) *learning.LearnedKnowledge {
	// 简单的知识提取逻辑
	if len(result.Content) < 50 {
		return nil // 内容太短，不提取
	}

	// 生成问题和答案
	question := result.Title
	if question == "" {
		question = result.Summary
	}

	// 确保问题以问号结尾
	if !strings.HasSuffix(question, "？") && !strings.HasSuffix(question, "?") {
		question += "？"
	}

	knowledge := &learning.LearnedKnowledge{
		Question:   question,
		Answer:     result.Summary,
		Category:   result.Category,
		Keywords:   result.Keywords,
		Context:    fmt.Sprintf("从网站爬取: %s", result.URL),
		Confidence: 0.7, // 爬取的知识置信度中等
		CreatedAt:  time.Now(),
		Metadata: map[string]interface{}{
			"source_url":     result.URL,
			"crawled_at":     result.CrawledAt,
			"target_id":      result.TargetID,
			"content_length": len(result.Content),
		},
	}

	return knowledge
}

// updateResultStatus 更新结果状态
func (kc *KnowledgeCrawler) updateResultStatus(resultID int, status string) error {
	query := "UPDATE crawl_results SET status = ?, processed_at = NOW() WHERE id = ?"
	_, err := kc.db.Exec(query, status, resultID)
	return err
}
