@echo off
echo 🕷️ 测试爬虫系统集成...
echo.

echo 📊 1. 检查爬虫仪表板页面...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8081/crawler_dashboard.html
echo.
echo.

echo 🔍 2. 测试爬虫状态API...
curl -X GET http://localhost:8081/api/crawler/status
echo.
echo.

echo 📋 3. 测试获取爬取目标API...
curl -X GET http://localhost:8081/api/crawler/targets
echo.
echo.

echo 📈 4. 测试获取统计信息API...
curl -X GET http://localhost:8081/api/crawler/statistics
echo.
echo.

echo 🚀 5. 测试启动爬虫API...
curl -X POST http://localhost:8081/api/crawler/start
echo.
echo.

echo ⏹️ 6. 测试停止爬虫API...
curl -X POST http://localhost:8081/api/crawler/stop
echo.
echo.

echo 📝 7. 测试添加爬取目标API...
curl -X POST http://localhost:8081/api/crawler/targets ^
  -H "Content-Type: application/json" ^
  -d "{\"name\":\"测试目标\",\"url\":\"https://example.com\",\"type\":\"website\",\"category\":\"test\",\"enabled\":true}"
echo.
echo.

echo 🎉 测试完成！
echo.
echo 📋 测试结果说明：
echo - 如果返回HTML内容，说明爬虫仪表板页面正常
echo - 如果API返回JSON数据，说明爬虫系统集成成功
echo - 如果返回404或连接错误，说明爬虫API尚未完全启用
echo.
echo 🌐 访问地址：
echo - 爬虫仪表板: http://localhost:8081/crawler_dashboard.html
echo - 主页: http://localhost:8081/
echo - 学习仪表板: http://localhost:8081/learning_dashboard.html
echo.
pause
