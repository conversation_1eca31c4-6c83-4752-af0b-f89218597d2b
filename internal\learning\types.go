package learning

import (
	"encoding/json"
	"time"
)

// QueryRecord 用户查询记录
type QueryRecord struct {
	ID             int64           `json:"id" db:"id"`
	SessionID      string          `json:"session_id" db:"session_id"`
	UserID         string          `json:"user_id" db:"user_id"`
	QueryText      string          `json:"query_text" db:"query_text"`
	QueryIntent    string          `json:"query_intent" db:"query_intent"`
	QueryType      string          `json:"query_type" db:"query_type"`
	QueryEmbedding json.RawMessage `json:"query_embedding" db:"query_embedding"`
	ContextData    json.RawMessage `json:"context_data" db:"context_data"`
	CreatedAt      time.Time       `json:"created_at" db:"created_at"`
}

// ResponseRecord 系统响应记录
type ResponseRecord struct {
	ID               int64     `json:"id" db:"id"`
	QueryID          int64     `json:"query_id" db:"query_id"`
	MatchedFAQID     *int      `json:"matched_faq_id" db:"matched_faq_id"`
	ResponseText     string    `json:"response_text" db:"response_text"`
	ResponseSource   string    `json:"response_source" db:"response_source"`
	ConfidenceScore  *float32  `json:"confidence_score" db:"confidence_score"`
	MatchType        string    `json:"match_type" db:"match_type"`
	ProcessingTimeMs int       `json:"processing_time_ms" db:"processing_time_ms"`
	CreatedAt        time.Time `json:"created_at" db:"created_at"`
}

// FeedbackRecord 用户反馈记录
type FeedbackRecord struct {
	ID                     int64     `json:"id" db:"id"`
	QueryID                int64     `json:"query_id" db:"query_id"`
	ResponseID             int64     `json:"response_id" db:"response_id"`
	FeedbackType           string    `json:"feedback_type" db:"feedback_type"`
	Rating                 *int      `json:"rating" db:"rating"`
	FeedbackText           string    `json:"feedback_text" db:"feedback_text"`
	ImprovementSuggestion  string    `json:"improvement_suggestion" db:"improvement_suggestion"`
	CreatedAt              time.Time `json:"created_at" db:"created_at"`
}

// BehaviorRecord 用户行为记录
type BehaviorRecord struct {
	ID           int64           `json:"id" db:"id"`
	QueryID      int64           `json:"query_id" db:"query_id"`
	BehaviorType string          `json:"behavior_type" db:"behavior_type"`
	BehaviorData json.RawMessage `json:"behavior_data" db:"behavior_data"`
	TimestampMs  int64           `json:"timestamp_ms" db:"timestamp_ms"`
	CreatedAt    time.Time       `json:"created_at" db:"created_at"`
}

// LearningPattern 学习模式
type LearningPattern struct {
	ID          int64           `json:"id" db:"id"`
	PatternType string          `json:"pattern_type" db:"pattern_type"`
	PatternName string          `json:"pattern_name" db:"pattern_name"`
	PatternData json.RawMessage `json:"pattern_data" db:"pattern_data"`
	Confidence  float32         `json:"confidence" db:"confidence"`
	UsageCount  int             `json:"usage_count" db:"usage_count"`
	SuccessRate float32         `json:"success_rate" db:"success_rate"`
	LastUpdated time.Time       `json:"last_updated" db:"last_updated"`
	CreatedAt   time.Time       `json:"created_at" db:"created_at"`
}

// FAQPerformance FAQ性能统计
type FAQPerformance struct {
	ID              int64      `json:"id" db:"id"`
	FAQID           int        `json:"faq_id" db:"faq_id"`
	QueryCount      int        `json:"query_count" db:"query_count"`
	MatchCount      int        `json:"match_count" db:"match_count"`
	PositiveFeedback int       `json:"positive_feedback" db:"positive_feedback"`
	NegativeFeedback int       `json:"negative_feedback" db:"negative_feedback"`
	AvgConfidence   float32    `json:"avg_confidence" db:"avg_confidence"`
	AvgRating       float32    `json:"avg_rating" db:"avg_rating"`
	LastMatched     *time.Time `json:"last_matched" db:"last_matched"`
	CreatedAt       time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at" db:"updated_at"`
}

// LearningConfig 学习配置
type LearningConfig struct {
	ID          int       `json:"id" db:"id"`
	ConfigKey   string    `json:"config_key" db:"config_key"`
	ConfigValue string    `json:"config_value" db:"config_value"`
	ConfigType  string    `json:"config_type" db:"config_type"`
	Description string    `json:"description" db:"description"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// LearningMetrics 学习指标
type LearningMetrics struct {
	TotalQueries        int64   `json:"total_queries"`
	TotalResponses      int64   `json:"total_responses"`
	TotalFeedback       int64   `json:"total_feedback"`
	PositiveFeedbackRate float32 `json:"positive_feedback_rate"`
	AvgConfidenceScore  float32 `json:"avg_confidence_score"`
	AvgProcessingTime   float32 `json:"avg_processing_time"`
	TopPerformingFAQs   []int   `json:"top_performing_faqs"`
	LearningPatterns    int     `json:"learning_patterns_count"`
}

// QueryContext 查询上下文
type QueryContext struct {
	SessionID       string            `json:"session_id"`
	UserID          string            `json:"user_id"`
	PreviousQueries []string          `json:"previous_queries"`
	UserPreferences map[string]string `json:"user_preferences"`
	Timestamp       time.Time         `json:"timestamp"`
}

// BehaviorData 行为数据
type BehaviorData struct {
	ElementID    string                 `json:"element_id,omitempty"`
	Position     map[string]int         `json:"position,omitempty"`
	Duration     int64                  `json:"duration,omitempty"`
	ScrollDepth  float32                `json:"scroll_depth,omitempty"`
	ClickCount   int                    `json:"click_count,omitempty"`
	CustomData   map[string]interface{} `json:"custom_data,omitempty"`
}

// PatternData 模式数据结构
type PatternData struct {
	// 查询相似性模式
	SimilarQueries []SimilarQuery `json:"similar_queries,omitempty"`
	
	// 意图映射模式
	IntentMapping map[string]float32 `json:"intent_mapping,omitempty"`
	
	// 响应优化模式
	OptimizedResponses []OptimizedResponse `json:"optimized_responses,omitempty"`
	
	// 用户偏好模式
	UserPreferences map[string]interface{} `json:"user_preferences,omitempty"`
}

// SimilarQuery 相似查询
type SimilarQuery struct {
	Query      string  `json:"query"`
	Similarity float32 `json:"similarity"`
	FAQID      int     `json:"faq_id"`
	Count      int     `json:"count"`
}

// OptimizedResponse 优化响应
type OptimizedResponse struct {
	OriginalResponse string  `json:"original_response"`
	OptimizedResponse string `json:"optimized_response"`
	ImprovementScore float32 `json:"improvement_score"`
	UsageCount       int     `json:"usage_count"`
}

// LearningEvent 学习事件
type LearningEvent struct {
	Type      string                 `json:"type"`
	QueryID   int64                  `json:"query_id"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
}
