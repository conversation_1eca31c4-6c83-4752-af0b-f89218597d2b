<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爬虫管理仪表板 - FAQ系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .dashboard {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #4facfe;
        }

        .stat-card h3 {
            color: #333;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: #666;
            font-size: 1.1em;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .targets-section {
            margin-top: 30px;
        }

        .section-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }

        .targets-grid {
            display: grid;
            gap: 20px;
        }

        .target-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #28a745;
        }

        .target-card.disabled {
            border-left-color: #dc3545;
            opacity: 0.7;
        }

        .target-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .target-name {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
        }

        .target-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .target-info {
            color: #666;
            margin-bottom: 10px;
        }

        .target-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.9em;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕷️ 爬虫管理仪表板</h1>
            <p>智能知识爬取与管理系统</p>
        </div>

        <div class="dashboard">
            <!-- 统计信息 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalTargets">-</h3>
                    <p>爬取目标</p>
                </div>
                <div class="stat-card">
                    <h3 id="activeTargets">-</h3>
                    <p>活跃目标</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalCrawls">-</h3>
                    <p>总爬取次数</p>
                </div>
                <div class="stat-card">
                    <h3 id="successRate">-</h3>
                    <p>成功率</p>
                </div>
            </div>

            <!-- 控制按钮 -->
            <div class="controls">
                <button class="btn btn-success" onclick="startCrawler()">🚀 启动爬虫</button>
                <button class="btn btn-danger" onclick="stopCrawler()">⏹️ 停止爬虫</button>
                <button class="btn btn-primary" onclick="refreshData()">🔄 刷新数据</button>
                <button class="btn btn-primary" onclick="showAddTargetForm()">➕ 添加目标</button>
            </div>

            <!-- 消息显示区域 -->
            <div id="messageArea"></div>

            <!-- 爬取目标列表 -->
            <div class="targets-section">
                <h2 class="section-title">爬取目标</h2>
                <div id="targetsContainer" class="loading">
                    正在加载爬取目标...
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let crawlerStatus = { running: false, targets: 0 };
        let targets = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            // 每30秒自动刷新一次
            setInterval(refreshData, 30000);
        });

        // 刷新数据
        async function refreshData() {
            try {
                await Promise.all([
                    loadCrawlerStatus(),
                    loadTargets(),
                    loadStatistics()
                ]);
            } catch (error) {
                showMessage('加载数据失败: ' + error.message, 'error');
            }
        }

        // 加载爬虫状态
        async function loadCrawlerStatus() {
            try {
                const response = await fetch('/api/crawler/status');
                if (response.ok) {
                    crawlerStatus = await response.json();
                }
            } catch (error) {
                console.error('加载爬虫状态失败:', error);
            }
        }

        // 加载爬取目标
        async function loadTargets() {
            try {
                const response = await fetch('/api/crawler/targets');
                if (response.ok) {
                    const data = await response.json();
                    targets = data.data || [];
                    renderTargets();
                } else {
                    document.getElementById('targetsContainer').innerHTML = 
                        '<div class="error">无法加载爬取目标，请检查爬虫服务是否正常运行</div>';
                }
            } catch (error) {
                document.getElementById('targetsContainer').innerHTML = 
                    '<div class="error">连接爬虫服务失败，API可能尚未启用</div>';
            }
        }

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('/api/crawler/statistics');
                if (response.ok) {
                    const data = await response.json();
                    updateStatistics(data.data);
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
                // 使用默认值
                updateStatistics({
                    total_targets: targets.length,
                    active_targets: targets.filter(t => t.enabled).length,
                    total_crawls: 0,
                    successful_crawls: 0
                });
            }
        }

        // 更新统计信息显示
        function updateStatistics(stats) {
            document.getElementById('totalTargets').textContent = stats.total_targets || 0;
            document.getElementById('activeTargets').textContent = stats.active_targets || 0;
            document.getElementById('totalCrawls').textContent = stats.total_crawls || 0;
            
            const successRate = stats.total_crawls > 0 
                ? Math.round((stats.successful_crawls / stats.total_crawls) * 100) 
                : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        // 渲染爬取目标
        function renderTargets() {
            const container = document.getElementById('targetsContainer');
            
            if (targets.length === 0) {
                container.innerHTML = '<div class="loading">暂无爬取目标</div>';
                return;
            }

            const html = targets.map(target => `
                <div class="target-card ${target.enabled ? '' : 'disabled'}">
                    <div class="target-header">
                        <div class="target-name">${target.name}</div>
                        <div class="target-status ${target.enabled ? 'status-active' : 'status-inactive'}">
                            ${target.enabled ? '启用' : '禁用'}
                        </div>
                    </div>
                    <div class="target-info">
                        <div><strong>URL:</strong> ${target.url}</div>
                        <div><strong>类型:</strong> ${target.type}</div>
                        <div><strong>分类:</strong> ${target.category}</div>
                        <div><strong>调度:</strong> ${target.schedule}</div>
                        ${target.last_crawled ? `<div><strong>最后爬取:</strong> ${new Date(target.last_crawled).toLocaleString()}</div>` : ''}
                    </div>
                    <div class="target-actions">
                        <button class="btn btn-primary btn-sm" onclick="manualCrawl(${target.id})">手动爬取</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteTarget(${target.id})">删除</button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 启动爬虫
        async function startCrawler() {
            try {
                const response = await fetch('/api/crawler/start', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('爬虫启动成功', 'success');
                    refreshData();
                } else {
                    showMessage('启动失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('启动爬虫失败: 爬虫API尚未启用', 'error');
            }
        }

        // 停止爬虫
        async function stopCrawler() {
            try {
                const response = await fetch('/api/crawler/stop', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('爬虫停止成功', 'success');
                    refreshData();
                } else {
                    showMessage('停止失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('停止爬虫失败: 爬虫API尚未启用', 'error');
            }
        }

        // 手动爬取
        async function manualCrawl(targetId) {
            try {
                const response = await fetch(`/api/crawler/targets/${targetId}/crawl`, { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('手动爬取任务已启动', 'success');
                } else {
                    showMessage('启动失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('手动爬取失败: 爬虫API尚未启用', 'error');
            }
        }

        // 删除目标
        async function deleteTarget(targetId) {
            if (!confirm('确定要删除这个爬取目标吗？')) {
                return;
            }

            try {
                const response = await fetch(`/api/crawler/targets/${targetId}`, { method: 'DELETE' });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('目标删除成功', 'success');
                    refreshData();
                } else {
                    showMessage('删除失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('删除目标失败: 爬虫API尚未启用', 'error');
            }
        }

        // 显示添加目标表单
        function showAddTargetForm() {
            showMessage('添加目标功能开发中...', 'error');
        }

        // 显示消息
        function showMessage(message, type) {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            
            messageArea.innerHTML = '';
            messageArea.appendChild(messageDiv);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }
    </script>
</body>
</html>
