package answer

import (
	"fmt"
	"strings"

	"faq-system/internal/mysql"
	"faq-system/internal/vectorstore"
)

// Generator 答案生成器 - 从原main.go迁移核心逻辑
type Generator struct {
	globalFAQs []mysql.FAQ
	templates  map[string]string
}

// NewAnswerGenerator 创建答案生成器 - 保持原有逻辑不变
func NewAnswerGenerator(faqs []mysql.FAQ) *Generator {
	templates := map[string]string{
		"high_confidence":   "%s",
		"medium_confidence": "%s\n\n💡 温馨提示：我对这个答案有 %.1f%% 的把握，如需更详细信息请继续提问。",
		"low_confidence":    "我找到了一些可能相关的信息（把握度：%.1f%%）：\n\n%s\n\n🤔 如果不完全符合您的需求，请：\n• 提供更多具体信息\n• 换个方式描述问题\n• 或告诉我您想了解的具体方面",
	}
	return &Generator{
		globalFAQs: faqs,
		templates:  templates,
	}
}

// GenerateIntelligentAnswer 基于向量搜索结果生成回答 - 保持原有逻辑不变
func (g *Generator) GenerateIntelligentAnswer(question string, results []vectorstore.SearchResult) (string, string) {
	// 注意：智能匹配已经在主函数中优先处理了，这里只处理向量搜索结果

	if len(results) == 0 {
		return g.GenerateFallbackAnswer(question), "系统异常"
	}

	// 根据搜索结果的ID获取对应的FAQ答案
	var answer string
	for _, faq := range g.globalFAQs {
		if faq.ID == results[0].ID {
			answer = faq.Answer
			break
		}
	}

	if answer == "" {
		return g.GenerateFallbackAnswer(question), "系统异常"
	}

	// 降低阈值，让系统更容易匹配
	// 高相似度（> 0.3）时，直接提供答案
	if results[0].Score > 0.3 {
		return answer, "FAQ数据库"
	}

	// 中等相似度（0.1-0.3）时，提供谨慎的回答
	if results[0].Score > 0.1 {
		return fmt.Sprintf("%s\n\n💡 温馨提示：我对这个答案有 %.1f%% 的把握，建议您可以进一步确认相关信息哦～",
			answer, results[0].Score*100), "FAQ数据库（高匹配）"
	}

	// 低相似度（0.05-0.1）时，提示不确定
	if results[0].Score > 0.05 {
		return fmt.Sprintf("嗯...我找到了一些可能相关的信息，不过我只有 %.1f%% 的把握：\n\n%s\n\n🤔 如果这个答案不太符合您的需求，您可以：\n• 换个方式问问看\n• 提供更多具体信息\n• 或者联系管理员获取更准确的答案",
			results[0].Score*100, answer), "FAQ数据库（低匹配）"
	}

	// 低相似度时，明确表示系统异常
	return g.GenerateFallbackAnswer(question), "系统异常"
}

// GenerateSmartAnswer 严密的智能匹配系统 - 保持原有逻辑不变
func (g *Generator) GenerateSmartAnswer(question string) string {
	original := strings.TrimSpace(question)
	q := strings.ToLower(original)

	// 第一层：处理空输入和极短输入
	if len(original) == 0 {
		return "🤔 您似乎没有输入任何内容，请告诉我您想了解什么技术问题吧！"
	}

	// 第二层：处理单字或极短输入（1-2个字符）
	if len([]rune(original)) <= 2 {
		// 常见单字处理
		switch q {
		case "你", "我", "他", "她", "它":
			return "🤔 请细说，希望能帮到您！我是技术FAQ助手，可以为您解答：\n\n• LocalAI和AI技术\n• MySQL数据库\n• 向量搜索\n• Go语言开发\n• 系统部署\n\n请详细描述您的问题～"
		case "好", "行", "嗯", "哦", "啊":
			return "😊 请细说您想了解的技术问题，我会尽力为您解答！"
		case "hi", "ok":
			return "👋 Hello! 请告诉我您想了解什么技术问题吧！"
		default:
			return "🤔 请细说，希望能帮到您！请详细描述您想了解的技术问题。"
		}
	}

	// 第三层：精确匹配问候语
	greetings := []string{"你好", "大家好", "各位好", "hello", "hi", "嗨", "您好"}
	for _, greeting := range greetings {
		if q == greeting || q == greeting+"！" || q == greeting+"!" {
			return "👋 您好！欢迎使用智能FAQ系统！\n\n我专注于技术领域，可以为您解答：\n• LocalAI和人工智能\n• MySQL数据库技术\n• 向量搜索原理\n• Go语言开发\n• 系统架构部署\n\n请随时提出您的技术问题！"
		}
	}

	// 第四层：精确匹配个人问题
	personalQuestions := []string{"你呢", "我呢", "你好吗", "你怎么样", "我怎么样"}
	for _, pq := range personalQuestions {
		if q == pq || q == pq+"？" || q == pq+"?" {
			return "😊 我是FAQ技术助手，专门为您解答技术问题！\n\n🤖 我的专长：\n• LocalAI技术咨询\n• 数据库解决方案\n• 编程语言指导\n• 系统架构建议\n\n有什么技术问题需要我帮助吗？"
		}
	}

	// 第五层：身份询问
	if strings.Contains(q, "你是谁") || strings.Contains(q, "你叫什么") ||
		q == "你是" || q == "你是什么" || q == "你是谁？" || q == "你是什么？" {
		return "🤖 我是智能FAQ技术助手，专门为您提供技术咨询服务！\n\n💡 我可以帮您：\n• 解答技术疑问\n• 提供解决方案\n• 推荐学习资源\n• 分析技术选型\n\n请告诉我您遇到的技术问题！"
	}

	// 第六层：精确技术关键词匹配
	return g.matchTechnicalKeywords(q)
}

// matchTechnicalKeywords 匹配技术关键词 - 保持原有逻辑不变
func (g *Generator) matchTechnicalKeywords(q string) string {
	// LocalAI相关 - 严格匹配
	if q == "localai" || q == "local ai" || strings.HasPrefix(q, "什么是localai") || strings.HasPrefix(q, "localai是什么") {
		return "LocalAI是一个开源的本地AI推理引擎，让您可以在自己的设备上运行大语言模型。\n\n🌟 主要特点：\n• 完全本地化部署，保护数据隐私\n• 支持多种AI模型格式（GGML、GGUF等）\n• 兼容OpenAI API，易于集成\n• 可以运行文本生成、图像生成、语音识别等任务\n• 开源免费，社区活跃\n\n💡 简单来说，LocalAI就是让您在本地拥有自己的ChatGPT！"
	}

	// MySQL相关问题
	if strings.Contains(q, "mysql") || strings.Contains(q, "数据库") {
		return "MySQL是世界上最流行的开源关系型数据库管理系统！\n\n🗄️ 在这个FAQ系统中，MySQL负责：\n• 存储所有的FAQ问题和答案\n• 管理用户查询历史\n• 提供可靠的数据持久化\n• 支持复杂的数据查询操作\n\n✨ 系统会自动创建数据库结构，您无需手动配置，开箱即用！"
	}

	// 向量搜索相关
	if strings.Contains(q, "向量") || strings.Contains(q, "embedding") || strings.Contains(q, "搜索") {
		return "向量搜索是现代AI系统的核心技术，让计算机能够理解文本的语义！\n\n🧠 工作原理：\n• 将文本转换为高维数字向量（embedding）\n• 通过计算向量间的相似度找到相关内容\n• 即使用词不同，语义相似的内容也能被找到\n\n🎯 在这个FAQ系统中：\n• 每个FAQ都有对应的向量表示\n• 用户问题也会转换为向量\n• 通过余弦相似度等算法进行智能匹配\n\n这就是为什么系统能理解您问题背后的真实意图！"
	}

	// Go语言相关 - 使用更精确的匹配
	if strings.Contains(q, "golang") || strings.Contains(q, "go语言") || strings.Contains(q, "go 语言") ||
		(strings.Contains(q, "go") && (strings.Contains(q, "语言") || strings.Contains(q, "编程") || strings.Contains(q, "开发"))) {
		return "Go语言是Google开发的现代编程语言，这个FAQ系统就是用Go构建的！\n\n🚀 Go语言的优势：\n• 语法简洁清晰，学习曲线平缓\n• 内置强大的并发支持（goroutines）\n• 编译速度极快，运行效率高\n• 丰富的标准库，开发效率高\n• 跨平台支持，部署简单\n\n💪 特别适合构建：\n• Web服务和API\n• 微服务架构\n• 云原生应用\n• 分布式系统\n\n这就是为什么我们选择Go来构建这个智能FAQ系统！"
	}

	// 继续其他技术匹配...
	return g.handleAdvancedTechnical(q)
}

// handleAdvancedTechnical 处理高级技术关键词 - 保持原有逻辑不变
func (g *Generator) handleAdvancedTechnical(q string) string {
	// 人工智能相关通用知识
	if strings.Contains(q, "人工智能") || strings.Contains(q, "ai") || strings.Contains(q, "机器学习") || strings.Contains(q, "深度学习") {
		return "人工智能（AI）是当今最热门的技术领域之一！\n\n🤖 AI的核心概念：\n• 让机器模拟人类智能行为\n• 通过数据学习和模式识别\n• 能够自主决策和解决问题\n\n🔥 主要技术分支：\n• 机器学习：从数据中学习规律\n• 深度学习：模拟神经网络结构\n• 自然语言处理：理解和生成文本\n• 计算机视觉：识别和理解图像\n\n💡 实际应用：\n• 智能助手（如ChatGPT、Siri）\n• 自动驾驶汽车\n• 医疗诊断辅助\n• 金融风险控制\n• 推荐系统\n\n就像这个FAQ系统，也运用了AI技术来理解您的问题并提供智能回答！"
	}

	// 数据库相关通用知识
	if (strings.Contains(q, "数据库") || strings.Contains(q, "database")) && !strings.Contains(q, "mysql") {
		return "数据库是现代信息系统的核心基础设施！\n\n📊 数据库的作用：\n• 持久化存储数据\n• 提供高效的数据查询\n• 确保数据的一致性和完整性\n• 支持并发访问和事务处理\n\n🗄️ 主要类型：\n• 关系型数据库：MySQL、PostgreSQL、Oracle\n• 非关系型数据库：MongoDB、Redis、Cassandra\n• 图数据库：Neo4j、ArangoDB\n• 时序数据库：InfluxDB、TimescaleDB\n\n⚡ 选择建议：\n• 传统业务系统：选择MySQL/PostgreSQL\n• 高并发场景：考虑Redis缓存\n• 大数据分析：选择列式数据库\n• 复杂关系：考虑图数据库\n\n这个FAQ系统就使用MySQL来存储问答数据，保证数据的可靠性！"
	}

	// 编程相关通用知识
	if strings.Contains(q, "编程") || strings.Contains(q, "程序") || strings.Contains(q, "代码") || strings.Contains(q, "开发") {
		return "编程是创造数字世界的魔法！\n\n💻 编程的本质：\n• 用计算机能理解的语言描述解决方案\n• 将复杂问题分解为简单步骤\n• 通过逻辑思维构建软件系统\n\n🌟 热门编程语言：\n• Python：简单易学，AI/数据科学首选\n• JavaScript：Web开发必备\n• Java：企业级应用的主力\n• Go：云原生和微服务的新宠\n• Rust：系统编程的安全选择\n\n🚀 学习建议：\n• 选择一门语言深入学习\n• 多做项目实践\n• 学习算法和数据结构\n• 关注开源社区\n• 培养解决问题的思维\n\n记住：编程不仅是技术，更是一种思维方式！"
	}

	// 技术学习相关
	if strings.Contains(q, "学习") || strings.Contains(q, "入门") || strings.Contains(q, "教程") {
		return "学习技术是一个持续的旅程！\n\n📚 高效学习方法：\n• 理论与实践相结合\n• 从简单项目开始\n• 多看优秀的开源代码\n• 加入技术社区交流\n• 写技术博客总结经验\n\n🎯 学习路径建议：\n• 基础知识：计算机原理、数据结构\n• 编程语言：选择1-2门深入掌握\n• 框架工具：根据方向选择主流框架\n• 项目实战：完成端到端的项目\n• 持续进阶：关注新技术趋势\n\n💡 学习资源：\n• 官方文档（最权威）\n• GitHub开源项目\n• 技术博客和教程\n• 在线课程平台\n• 技术会议和讲座\n\n坚持学习，技术改变世界，也改变你的未来！"
	}

	// 非技术问题处理
	return g.handleNonTechnical(q)
}

// handleNonTechnical 处理非技术问题 - 保持原有逻辑不变
func (g *Generator) handleNonTechnical(q string) string {
	// 生活类问题 - 精确匹配
	cookingKeywords := []string{"做饭", "菜谱", "烹饪", "红烧肉", "炒菜", "煮饭", "食谱"}
	for _, keyword := range cookingKeywords {
		if strings.Contains(q, keyword) {
			return "🍳 我是技术FAQ助手，专注于技术领域，不太懂烹饪呢！\n\n👨‍🍳 烹饪建议：\n• 下载美食类APP（如下厨房）\n• 搜索菜谱网站\n• 观看烹饪视频教程\n• 请教身边的美食达人\n\n不过如果您想了解美食网站的技术架构，我很乐意分享！"
		}
	}

	// 天气类问题 - 精确匹配
	if strings.Contains(q, "天气") || strings.Contains(q, "weather") || strings.Contains(q, "下雨") || strings.Contains(q, "晴天") {
		return "🌤️ 我是技术FAQ助手，无法提供天气信息。\n\n💡 获取天气信息：\n• 查看手机天气应用\n• 访问天气预报网站\n• 询问语音助手\n\n有什么技术问题需要我帮助吗？"
	}

	// 时间类问题 - 精确匹配
	if strings.Contains(q, "几点") || strings.Contains(q, "现在时间") || q == "time" {
		return "⏰ 我是技术FAQ助手，无法提供时间信息。\n\n🕐 查看时间：\n• 看手机或电脑时钟\n• 询问语音助手\n\n请细说您想了解的技术问题！"
	}

	// 兜底处理 - 根据问题特征给出不同回应
	// 包含疑问词的问题
	if strings.Contains(q, "什么") || strings.Contains(q, "如何") || strings.Contains(q, "怎么") ||
		strings.Contains(q, "为什么") || strings.Contains(q, "哪个") || strings.Contains(q, "哪些") {
		return "🤔 请细说，希望能帮到您！我专注于技术领域：\n\n💻 我可以解答：\n• LocalAI和AI技术\n• MySQL数据库\n• 向量搜索\n• Go语言开发\n• 系统架构\n\n请详细描述您的技术问题～"
	}

	// 其他情况 - 通用兜底
	return "🤔 请细说，希望能帮到您！我是技术FAQ助手，请告诉我您想了解的具体技术问题。"
}

// GenerateFallbackAnswer 生成兜底回答 - 保持原有逻辑不变
func (g *Generator) GenerateFallbackAnswer(question string) string {
	q := strings.ToLower(strings.TrimSpace(question))

	// 根据问题类型给出不同的兜底回答
	if strings.Contains(q, "什么") || strings.Contains(q, "what") {
		return "🤔 这是个很好的问题！虽然我的专业领域是技术相关，但我建议：\n\n💡 如果是技术问题：\n• 尝试更具体地描述问题\n• 提供相关的技术背景\n• 我会尽力为您解答\n\n🌐 如果是其他问题：\n• 可以尝试搜索引擎\n• 咨询相关领域的专家\n• 或查看专业网站\n\n我专注于LocalAI、数据库、编程等技术话题！"
	}

	if strings.Contains(q, "如何") || strings.Contains(q, "怎么") || strings.Contains(q, "how") {
		return "🛠️ 看起来您需要操作指导！\n\n💻 如果是技术操作：\n• 请提供更多技术细节\n• 说明您的使用环境\n• 我会尽力提供技术指导\n\n📚 如果是其他操作：\n• 查看相关官方文档\n• 搜索专业教程\n• 咨询相关领域专家\n\n我擅长技术相关的操作指导！"
	}

	// 默认兜底回答
	return "😊 感谢您的提问！我是专注于技术领域的FAQ助手。\n\n🤖 我的专长领域：\n• LocalAI和AI技术\n• 数据库和存储\n• 编程语言和开发\n• 系统架构和部署\n\n🌟 如果您有技术问题：\n• 请尽量详细描述问题\n• 我会基于技术知识为您解答\n\n如果是其他领域的问题，建议咨询相关专业人士哦！"
}
