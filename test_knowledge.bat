@echo off
echo 🧪 测试FAQ系统知识学习功能...
echo.

echo 📚 1. 教学新知识...
curl -X POST http://localhost:8081/api/knowledge/teach ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是Docker？\",\"answer\":\"Docker是一个开源的容器化平台，可以将应用程序及其依赖项打包到轻量级、可移植的容器中。\",\"user_id\":\"test_user\"}"
echo.
echo.

echo 🤔 2. 测试问答 - Docker...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是Docker？\"}"
echo.
echo.

echo 🤔 3. 测试问答 - Python...
curl -X POST http://localhost:8081/ask ^
  -H "Content-Type: application/json" ^
  -d "{\"question\":\"什么是Python？\"}"
echo.
echo.

echo 📋 4. 获取学习知识列表...
curl -X GET http://localhost:8081/api/knowledge/list
echo.
echo.

echo 🎉 测试完成！
pause
