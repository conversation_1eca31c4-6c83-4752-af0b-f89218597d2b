package algorithms

import (
	"fmt"
	"strings"
)

// SortResult 排序结果
type SortResult struct {
	Algorithm    string
	OriginalData []int
	SortedData   []int
	Steps        []string
	Comparisons  int
	Swaps        int
	TimeComplexity string
	SpaceComplexity string
}

// SortingEngine 排序引擎
type SortingEngine struct{}

// NewSortingEngine 创建排序引擎
func NewSortingEngine() *SortingEngine {
	return &SortingEngine{}
}

// BubbleSort 冒泡排序
func (se *SortingEngine) BubbleSort(data []int) *SortResult {
	result := &SortResult{
		Algorithm:       "冒泡排序 (Bubble Sort)",
		OriginalData:    make([]int, len(data)),
		Steps:           []string{},
		Comparisons:     0,
		Swaps:          0,
		TimeComplexity:  "O(n²)",
		SpaceComplexity: "O(1)",
	}
	
	copy(result.OriginalData, data)
	arr := make([]int, len(data))
	copy(arr, data)
	
	result.Steps = append(result.Steps, fmt.Sprintf("初始数组: %v", arr))
	
	n := len(arr)
	for i := 0; i < n-1; i++ {
		swapped := false
		result.Steps = append(result.Steps, fmt.Sprintf("第 %d 轮排序:", i+1))
		
		for j := 0; j < n-i-1; j++ {
			result.Comparisons++
			if arr[j] > arr[j+1] {
				// 交换
				arr[j], arr[j+1] = arr[j+1], arr[j]
				result.Swaps++
				swapped = true
				result.Steps = append(result.Steps, 
					fmt.Sprintf("  比较 %d 和 %d，交换后: %v", arr[j+1], arr[j], arr))
			} else {
				result.Steps = append(result.Steps, 
					fmt.Sprintf("  比较 %d 和 %d，无需交换", arr[j], arr[j+1]))
			}
		}
		
		if !swapped {
			result.Steps = append(result.Steps, "  本轮无交换，排序完成")
			break
		}
	}
	
	result.SortedData = arr
	result.Steps = append(result.Steps, fmt.Sprintf("最终结果: %v", arr))
	
	return result
}

// QuickSort 快速排序
func (se *SortingEngine) QuickSort(data []int) *SortResult {
	result := &SortResult{
		Algorithm:       "快速排序 (Quick Sort)",
		OriginalData:    make([]int, len(data)),
		Steps:           []string{},
		Comparisons:     0,
		Swaps:          0,
		TimeComplexity:  "O(n log n) 平均, O(n²) 最坏",
		SpaceComplexity: "O(log n)",
	}
	
	copy(result.OriginalData, data)
	arr := make([]int, len(data))
	copy(arr, data)
	
	result.Steps = append(result.Steps, fmt.Sprintf("初始数组: %v", arr))
	
	se.quickSortHelper(arr, 0, len(arr)-1, result, 1)
	
	result.SortedData = arr
	result.Steps = append(result.Steps, fmt.Sprintf("最终结果: %v", arr))
	
	return result
}

// quickSortHelper 快速排序辅助函数
func (se *SortingEngine) quickSortHelper(arr []int, low, high int, result *SortResult, depth int) {
	if low < high {
		indent := strings.Repeat("  ", depth-1)
		result.Steps = append(result.Steps, 
			fmt.Sprintf("%s对子数组 [%d:%d] 进行快速排序", indent, low, high))
		
		// 分区
		pi := se.partition(arr, low, high, result, depth)
		
		result.Steps = append(result.Steps, 
			fmt.Sprintf("%s分区完成，基准位置: %d，数组: %v", indent, pi, arr))
		
		// 递归排序左右子数组
		se.quickSortHelper(arr, low, pi-1, result, depth+1)
		se.quickSortHelper(arr, pi+1, high, result, depth+1)
	}
}

// partition 分区函数
func (se *SortingEngine) partition(arr []int, low, high int, result *SortResult, depth int) int {
	pivot := arr[high]
	indent := strings.Repeat("  ", depth)
	result.Steps = append(result.Steps, 
		fmt.Sprintf("%s选择基准元素: %d", indent, pivot))
	
	i := low - 1
	
	for j := low; j < high; j++ {
		result.Comparisons++
		if arr[j] <= pivot {
			i++
			if i != j {
				arr[i], arr[j] = arr[j], arr[i]
				result.Swaps++
				result.Steps = append(result.Steps, 
					fmt.Sprintf("%s交换 %d 和 %d: %v", indent, arr[i], arr[j], arr))
			}
		}
	}
	
	arr[i+1], arr[high] = arr[high], arr[i+1]
	result.Swaps++
	result.Steps = append(result.Steps, 
		fmt.Sprintf("%s将基准 %d 放到正确位置: %v", indent, pivot, arr))
	
	return i + 1
}

// MergeSort 归并排序
func (se *SortingEngine) MergeSort(data []int) *SortResult {
	result := &SortResult{
		Algorithm:       "归并排序 (Merge Sort)",
		OriginalData:    make([]int, len(data)),
		Steps:           []string{},
		Comparisons:     0,
		Swaps:          0,
		TimeComplexity:  "O(n log n)",
		SpaceComplexity: "O(n)",
	}
	
	copy(result.OriginalData, data)
	arr := make([]int, len(data))
	copy(arr, data)
	
	result.Steps = append(result.Steps, fmt.Sprintf("初始数组: %v", arr))
	
	se.mergeSortHelper(arr, 0, len(arr)-1, result, 1)
	
	result.SortedData = arr
	result.Steps = append(result.Steps, fmt.Sprintf("最终结果: %v", arr))
	
	return result
}

// mergeSortHelper 归并排序辅助函数
func (se *SortingEngine) mergeSortHelper(arr []int, left, right int, result *SortResult, depth int) {
	if left < right {
		indent := strings.Repeat("  ", depth-1)
		result.Steps = append(result.Steps, 
			fmt.Sprintf("%s分解数组 [%d:%d]", indent, left, right))
		
		mid := (left + right) / 2
		
		// 递归排序左右两半
		se.mergeSortHelper(arr, left, mid, result, depth+1)
		se.mergeSortHelper(arr, mid+1, right, result, depth+1)
		
		// 合并
		se.merge(arr, left, mid, right, result, depth)
	}
}

// merge 合并函数
func (se *SortingEngine) merge(arr []int, left, mid, right int, result *SortResult, depth int) {
	indent := strings.Repeat("  ", depth-1)
	result.Steps = append(result.Steps, 
		fmt.Sprintf("%s合并 [%d:%d] 和 [%d:%d]", indent, left, mid, mid+1, right))
	
	// 创建临时数组
	leftArr := make([]int, mid-left+1)
	rightArr := make([]int, right-mid)
	
	copy(leftArr, arr[left:mid+1])
	copy(rightArr, arr[mid+1:right+1])
	
	i, j, k := 0, 0, left
	
	// 合并两个有序数组
	for i < len(leftArr) && j < len(rightArr) {
		result.Comparisons++
		if leftArr[i] <= rightArr[j] {
			arr[k] = leftArr[i]
			i++
		} else {
			arr[k] = rightArr[j]
			j++
		}
		k++
	}
	
	// 复制剩余元素
	for i < len(leftArr) {
		arr[k] = leftArr[i]
		i++
		k++
	}
	
	for j < len(rightArr) {
		arr[k] = rightArr[j]
		j++
		k++
	}
	
	result.Steps = append(result.Steps, 
		fmt.Sprintf("%s合并结果: %v", indent, arr[left:right+1]))
}

// InsertionSort 插入排序
func (se *SortingEngine) InsertionSort(data []int) *SortResult {
	result := &SortResult{
		Algorithm:       "插入排序 (Insertion Sort)",
		OriginalData:    make([]int, len(data)),
		Steps:           []string{},
		Comparisons:     0,
		Swaps:          0,
		TimeComplexity:  "O(n²)",
		SpaceComplexity: "O(1)",
	}
	
	copy(result.OriginalData, data)
	arr := make([]int, len(data))
	copy(arr, data)
	
	result.Steps = append(result.Steps, fmt.Sprintf("初始数组: %v", arr))
	
	for i := 1; i < len(arr); i++ {
		key := arr[i]
		j := i - 1
		
		result.Steps = append(result.Steps, 
			fmt.Sprintf("第 %d 轮：插入元素 %d", i, key))
		
		for j >= 0 {
			result.Comparisons++
			if arr[j] > key {
				arr[j+1] = arr[j]
				result.Swaps++
				j--
			} else {
				break
			}
		}
		
		arr[j+1] = key
		result.Steps = append(result.Steps, 
			fmt.Sprintf("  插入后: %v", arr))
	}
	
	result.SortedData = arr
	result.Steps = append(result.Steps, fmt.Sprintf("最终结果: %v", arr))
	
	return result
}

// SelectionSort 选择排序
func (se *SortingEngine) SelectionSort(data []int) *SortResult {
	result := &SortResult{
		Algorithm:       "选择排序 (Selection Sort)",
		OriginalData:    make([]int, len(data)),
		Steps:           []string{},
		Comparisons:     0,
		Swaps:          0,
		TimeComplexity:  "O(n²)",
		SpaceComplexity: "O(1)",
	}
	
	copy(result.OriginalData, data)
	arr := make([]int, len(data))
	copy(arr, data)
	
	result.Steps = append(result.Steps, fmt.Sprintf("初始数组: %v", arr))
	
	n := len(arr)
	for i := 0; i < n-1; i++ {
		minIdx := i
		result.Steps = append(result.Steps, 
			fmt.Sprintf("第 %d 轮：寻找最小元素", i+1))
		
		for j := i + 1; j < n; j++ {
			result.Comparisons++
			if arr[j] < arr[minIdx] {
				minIdx = j
			}
		}
		
		if minIdx != i {
			arr[i], arr[minIdx] = arr[minIdx], arr[i]
			result.Swaps++
			result.Steps = append(result.Steps, 
				fmt.Sprintf("  交换 %d 和 %d: %v", arr[i], arr[minIdx], arr))
		} else {
			result.Steps = append(result.Steps, "  最小元素已在正确位置")
		}
	}
	
	result.SortedData = arr
	result.Steps = append(result.Steps, fmt.Sprintf("最终结果: %v", arr))
	
	return result
}

// GetSortingAlgorithms 获取支持的排序算法列表
func (se *SortingEngine) GetSortingAlgorithms() []string {
	return []string{
		"bubble", "quick", "merge", "insertion", "selection",
	}
}

// ExecuteSort 执行指定的排序算法
func (se *SortingEngine) ExecuteSort(algorithm string, data []int) *SortResult {
	switch strings.ToLower(algorithm) {
	case "bubble", "冒泡", "冒泡排序":
		return se.BubbleSort(data)
	case "quick", "快速", "快速排序":
		return se.QuickSort(data)
	case "merge", "归并", "归并排序":
		return se.MergeSort(data)
	case "insertion", "插入", "插入排序":
		return se.InsertionSort(data)
	case "selection", "选择", "选择排序":
		return se.SelectionSort(data)
	default:
		// 默认使用快速排序
		return se.QuickSort(data)
	}
}
