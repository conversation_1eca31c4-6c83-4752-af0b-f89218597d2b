#!/usr/bin/env python3
"""
测试FAQ系统的知识学习和匹配功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8081"

def test_teach_knowledge():
    """测试教学知识功能"""
    print("🧠 测试教学知识功能...")
    
    knowledge_data = {
        "question": "什么是Docker？",
        "answer": "Docker是一个开源的容器化平台，可以将应用程序及其依赖项打包到轻量级、可移植的容器中。",
        "user_id": "test_user"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/knowledge/teach", json=knowledge_data)
        if response.status_code == 200:
            print("✅ 知识教学成功")
            print(f"响应: {response.json()}")
        else:
            print(f"❌ 知识教学失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_ask_question(question):
    """测试问答功能"""
    print(f"🤔 测试问题: {question}")
    
    try:
        response = requests.post(f"{BASE_URL}/ask", json={"question": question})
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 回答: {result.get('answer', 'No answer')}")
            print(f"📊 来源: {result.get('source', 'Unknown')}")
            print(f"🎯 置信度: {result.get('confidence', 0):.2f}")
        else:
            print(f"❌ 问答失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_knowledge_list():
    """测试获取学习知识列表"""
    print("📚 测试获取学习知识列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/knowledge/list")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 找到 {len(result.get('knowledge', []))} 条学习知识")
            for i, knowledge in enumerate(result.get('knowledge', [])[:3]):
                print(f"  {i+1}. {knowledge.get('question', 'No question')} (置信度: {knowledge.get('confidence', 0):.2f})")
        else:
            print(f"❌ 获取知识列表失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    print("🧪 开始测试FAQ系统知识学习功能...")
    print("=" * 50)
    
    # 等待系统启动
    print("⏳ 等待系统启动...")
    time.sleep(2)
    
    # 测试健康检查
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 系统运行正常")
        else:
            print("❌ 系统未正常运行")
            return
    except Exception as e:
        print(f"❌ 无法连接到系统: {e}")
        return
    
    print("\n" + "=" * 50)
    
    # 1. 测试教学知识
    test_teach_knowledge()
    
    print("\n" + "=" * 50)
    
    # 2. 等待一下让知识保存
    time.sleep(1)
    
    # 3. 测试获取知识列表
    test_knowledge_list()
    
    print("\n" + "=" * 50)
    
    # 4. 测试问答功能
    test_questions = [
        "什么是Docker？",
        "Docker是什么？", 
        "什么是Python？",
        "Python是什么？",
        "什么是C#？",
        "C#是什么？"
    ]
    
    for question in test_questions:
        test_ask_question(question)
        print("-" * 30)
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
