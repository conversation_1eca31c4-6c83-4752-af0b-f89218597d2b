package algorithms

import (
	"fmt"
	"sort"
	"strings"
)

// SearchResult 搜索结果
type SearchResult struct {
	Algorithm    string
	Target       int
	Data         []int
	Found        bool
	Index        int
	Steps        []string
	Comparisons  int
	TimeComplexity string
	SpaceComplexity string
}

// SearchingEngine 搜索引擎
type SearchingEngine struct{}

// NewSearchingEngine 创建搜索引擎
func NewSearchingEngine() *SearchingEngine {
	return &SearchingEngine{}
}

// LinearSearch 线性搜索
func (se *SearchingEngine) LinearSearch(data []int, target int) *SearchResult {
	result := &SearchResult{
		Algorithm:       "线性搜索 (Linear Search)",
		Target:          target,
		Data:            make([]int, len(data)),
		Found:           false,
		Index:           -1,
		Steps:           []string{},
		Comparisons:     0,
		TimeComplexity:  "O(n)",
		SpaceComplexity: "O(1)",
	}
	
	copy(result.Data, data)
	result.Steps = append(result.Steps, fmt.Sprintf("在数组 %v 中搜索目标值 %d", data, target))
	
	for i, value := range data {
		result.Comparisons++
		result.Steps = append(result.Steps, 
			fmt.Sprintf("第 %d 步：比较 data[%d] = %d 与目标值 %d", 
				result.Comparisons, i, value, target))
		
		if value == target {
			result.Found = true
			result.Index = i
			result.Steps = append(result.Steps, 
				fmt.Sprintf("找到目标值！位置: %d", i))
			return result
		}
	}
	
	result.Steps = append(result.Steps, "搜索完成，未找到目标值")
	return result
}

// BinarySearch 二分搜索
func (se *SearchingEngine) BinarySearch(data []int, target int) *SearchResult {
	result := &SearchResult{
		Algorithm:       "二分搜索 (Binary Search)",
		Target:          target,
		Data:            make([]int, len(data)),
		Found:           false,
		Index:           -1,
		Steps:           []string{},
		Comparisons:     0,
		TimeComplexity:  "O(log n)",
		SpaceComplexity: "O(1)",
	}
	
	copy(result.Data, data)
	
	// 检查数组是否已排序
	if !sort.IntsAreSorted(data) {
		result.Steps = append(result.Steps, "注意：二分搜索需要有序数组，先对数组排序")
		sort.Ints(data)
		result.Steps = append(result.Steps, fmt.Sprintf("排序后数组: %v", data))
	}
	
	result.Steps = append(result.Steps, 
		fmt.Sprintf("在有序数组 %v 中搜索目标值 %d", data, target))
	
	left, right := 0, len(data)-1
	
	for left <= right {
		mid := (left + right) / 2
		result.Comparisons++
		
		result.Steps = append(result.Steps, 
			fmt.Sprintf("第 %d 步：left=%d, right=%d, mid=%d, data[%d]=%d", 
				result.Comparisons, left, right, mid, mid, data[mid]))
		
		if data[mid] == target {
			result.Found = true
			result.Index = mid
			result.Steps = append(result.Steps, 
				fmt.Sprintf("找到目标值！位置: %d", mid))
			return result
		} else if data[mid] < target {
			left = mid + 1
			result.Steps = append(result.Steps, 
				fmt.Sprintf("data[%d] < %d，搜索右半部分", mid, target))
		} else {
			right = mid - 1
			result.Steps = append(result.Steps, 
				fmt.Sprintf("data[%d] > %d，搜索左半部分", mid, target))
		}
	}
	
	result.Steps = append(result.Steps, "搜索完成，未找到目标值")
	return result
}

// InterpolationSearch 插值搜索
func (se *SearchingEngine) InterpolationSearch(data []int, target int) *SearchResult {
	result := &SearchResult{
		Algorithm:       "插值搜索 (Interpolation Search)",
		Target:          target,
		Data:            make([]int, len(data)),
		Found:           false,
		Index:           -1,
		Steps:           []string{},
		Comparisons:     0,
		TimeComplexity:  "O(log log n) 平均, O(n) 最坏",
		SpaceComplexity: "O(1)",
	}
	
	copy(result.Data, data)
	
	// 检查数组是否已排序
	if !sort.IntsAreSorted(data) {
		result.Steps = append(result.Steps, "注意：插值搜索需要有序数组，先对数组排序")
		sort.Ints(data)
		result.Steps = append(result.Steps, fmt.Sprintf("排序后数组: %v", data))
	}
	
	result.Steps = append(result.Steps, 
		fmt.Sprintf("在有序数组 %v 中搜索目标值 %d", data, target))
	
	left, right := 0, len(data)-1
	
	for left <= right && target >= data[left] && target <= data[right] {
		if left == right {
			result.Comparisons++
			if data[left] == target {
				result.Found = true
				result.Index = left
				result.Steps = append(result.Steps, 
					fmt.Sprintf("找到目标值！位置: %d", left))
			}
			break
		}
		
		// 插值公式计算位置
		pos := left + ((target-data[left])*(right-left))/(data[right]-data[left])
		result.Comparisons++
		
		result.Steps = append(result.Steps, 
			fmt.Sprintf("第 %d 步：left=%d, right=%d, 插值位置=%d, data[%d]=%d", 
				result.Comparisons, left, right, pos, pos, data[pos]))
		
		if data[pos] == target {
			result.Found = true
			result.Index = pos
			result.Steps = append(result.Steps, 
				fmt.Sprintf("找到目标值！位置: %d", pos))
			return result
		} else if data[pos] < target {
			left = pos + 1
			result.Steps = append(result.Steps, 
				fmt.Sprintf("data[%d] < %d，搜索右半部分", pos, target))
		} else {
			right = pos - 1
			result.Steps = append(result.Steps, 
				fmt.Sprintf("data[%d] > %d，搜索左半部分", pos, target))
		}
	}
	
	if !result.Found {
		result.Steps = append(result.Steps, "搜索完成，未找到目标值")
	}
	
	return result
}

// ExponentialSearch 指数搜索
func (se *SearchingEngine) ExponentialSearch(data []int, target int) *SearchResult {
	result := &SearchResult{
		Algorithm:       "指数搜索 (Exponential Search)",
		Target:          target,
		Data:            make([]int, len(data)),
		Found:           false,
		Index:           -1,
		Steps:           []string{},
		Comparisons:     0,
		TimeComplexity:  "O(log n)",
		SpaceComplexity: "O(1)",
	}
	
	copy(result.Data, data)
	
	// 检查数组是否已排序
	if !sort.IntsAreSorted(data) {
		result.Steps = append(result.Steps, "注意：指数搜索需要有序数组，先对数组排序")
		sort.Ints(data)
		result.Steps = append(result.Steps, fmt.Sprintf("排序后数组: %v", data))
	}
	
	result.Steps = append(result.Steps, 
		fmt.Sprintf("在有序数组 %v 中搜索目标值 %d", data, target))
	
	n := len(data)
	
	// 检查第一个元素
	if data[0] == target {
		result.Found = true
		result.Index = 0
		result.Comparisons = 1
		result.Steps = append(result.Steps, "第一个元素就是目标值！")
		return result
	}
	
	// 找到范围
	i := 1
	for i < n && data[i] <= target {
		result.Comparisons++
		result.Steps = append(result.Steps, 
			fmt.Sprintf("检查位置 %d: data[%d] = %d", i, i, data[i]))
		i *= 2
	}
	
	// 在找到的范围内进行二分搜索
	left := i / 2
	right := i
	if right >= n {
		right = n - 1
	}
	
	result.Steps = append(result.Steps, 
		fmt.Sprintf("确定搜索范围: [%d, %d]", left, right))
	
	// 二分搜索
	for left <= right {
		mid := (left + right) / 2
		result.Comparisons++
		
		result.Steps = append(result.Steps, 
			fmt.Sprintf("二分搜索：left=%d, right=%d, mid=%d, data[%d]=%d", 
				left, right, mid, mid, data[mid]))
		
		if data[mid] == target {
			result.Found = true
			result.Index = mid
			result.Steps = append(result.Steps, 
				fmt.Sprintf("找到目标值！位置: %d", mid))
			return result
		} else if data[mid] < target {
			left = mid + 1
		} else {
			right = mid - 1
		}
	}
	
	result.Steps = append(result.Steps, "搜索完成，未找到目标值")
	return result
}

// GetSearchingAlgorithms 获取支持的搜索算法列表
func (se *SearchingEngine) GetSearchingAlgorithms() []string {
	return []string{
		"linear", "binary", "interpolation", "exponential",
	}
}

// ExecuteSearch 执行指定的搜索算法
func (se *SearchingEngine) ExecuteSearch(algorithm string, data []int, target int) *SearchResult {
	switch strings.ToLower(algorithm) {
	case "linear", "线性", "线性搜索", "顺序搜索":
		return se.LinearSearch(data, target)
	case "binary", "二分", "二分搜索", "折半搜索":
		return se.BinarySearch(data, target)
	case "interpolation", "插值", "插值搜索":
		return se.InterpolationSearch(data, target)
	case "exponential", "指数", "指数搜索":
		return se.ExponentialSearch(data, target)
	default:
		// 默认使用二分搜索
		return se.BinarySearch(data, target)
	}
}

// FindAllOccurrences 查找所有出现位置
func (se *SearchingEngine) FindAllOccurrences(data []int, target int) []int {
	var positions []int
	for i, value := range data {
		if value == target {
			positions = append(positions, i)
		}
	}
	return positions
}

// FindMinMax 查找最小值和最大值
func (se *SearchingEngine) FindMinMax(data []int) (min, max int, minIdx, maxIdx int) {
	if len(data) == 0 {
		return 0, 0, -1, -1
	}
	
	min, max = data[0], data[0]
	minIdx, maxIdx = 0, 0
	
	for i, value := range data {
		if value < min {
			min = value
			minIdx = i
		}
		if value > max {
			max = value
			maxIdx = i
		}
	}
	
	return min, max, minIdx, maxIdx
}
