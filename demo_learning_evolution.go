package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	"faq-system/internal/config"
	"faq-system/internal/learning"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🧠 FAQ系统智能学习进化演示")
	fmt.Println("====================================================")

	// 创建学习管理器
	manager := learning.NewManager(db)
	evolutionEngine := manager.GetEvolutionEngine()

	fmt.Println("\n📊 第一步：分析当前用户思维模式")
	fmt.Println("------------------------------")

	// 分析用户思维模式
	err = evolutionEngine.AnalyzeUserThinking()
	if err != nil {
		fmt.Printf("❌ 分析失败: %v\n", err)
	} else {
		fmt.Println("✅ 用户思维模式分析完成")
	}

	// 显示发现的模式
	showThinkingPatterns(db)

	fmt.Println("\n🚀 第二步：应用进化改进")
	fmt.Println("------------------------------")

	// 应用进化改进
	err = evolutionEngine.ApplyEvolutionaryImprovements()
	if err != nil {
		fmt.Printf("❌ 进化改进失败: %v\n", err)
	} else {
		fmt.Println("✅ 进化改进应用完成")
	}

	fmt.Println("\n🎯 第三步：测试智能匹配效果")
	fmt.Println("------------------------------")

	// 创建智能匹配器
	smartMatcher := learning.NewSmartMatcher(db)

	// 模拟不同类型的用户查询
	testCases := []struct {
		query       string
		userType    string
		expectation string
	}{
		{"什么是LocalAI？", "technical", "技术用户偏好详细回答"},
		{"LocalAI介绍", "general", "普通用户偏好简洁回答"},
		{"Go语言优势", "technical", "技术关键词应该被提升"},
		{"数据库存储", "business", "业务用户关注实用性"},
	}

	for i, testCase := range testCases {
		fmt.Printf("\n测试 %d: %s\n", i+1, testCase.query)

		// 创建匹配上下文
		context := &learning.MatchingContext{
			Query:         testCase.query,
			UserID:        fmt.Sprintf("test_user_%s", testCase.userType),
			Intent:        "technical_question",
			UserSegment:   testCase.userType,
			ThinkingStyle: "direct",
			SessionLength: 1,
			Metadata:      make(map[string]interface{}),
		}

		// 模拟原始匹配分数
		originalScore := float32(0.6)

		// 应用智能匹配
		result := smartMatcher.ApplySmartMatching(context, originalScore)

		fmt.Printf("  原始分数: %.3f\n", result.OriginalScore)
		fmt.Printf("  优化分数: %.3f (提升: %.1f%%)\n",
			result.AdjustedScore, (result.AdjustedScore-result.OriginalScore)*100)
		fmt.Printf("  应用规则: %v\n", result.AppliedRules)
		fmt.Printf("  响应风格: %s\n", result.ResponseStyle)
		fmt.Printf("  期望效果: %s\n", testCase.expectation)

		if result.AdjustedScore > result.OriginalScore {
			fmt.Printf("  ✅ 智能优化生效\n")
		} else {
			fmt.Printf("  ⚠️  无优化效果\n")
		}
	}

	fmt.Println("\n📈 第四步：查看系统进化历史")
	fmt.Println("------------------------------")
	showEvolutionHistory(db)

	fmt.Println("\n🔄 第五步：模拟持续学习过程")
	fmt.Println("------------------------------")

	fmt.Println("模拟用户交互序列...")

	// 模拟一系列用户交互
	interactions := []struct {
		query    string
		feedback string
		rating   int
	}{
		{"什么是LocalAI？", "helpful", 5},
		{"LocalAI怎么用？", "not_helpful", 2},
		{"Go语言优势", "helpful", 4},
		{"数据库配置", "partially_helpful", 3},
	}

	for i, interaction := range interactions {
		fmt.Printf("交互 %d: %s -> %s (评分: %d)\n",
			i+1, interaction.query, interaction.feedback, interaction.rating)

		// 这里会触发学习系统记录和分析
		// 实际系统中，每次交互都会自动触发学习过程
	}

	fmt.Println("\n💡 学习效果总结:")
	fmt.Println("1. 🎯 系统识别了用户的查询模式和偏好")
	fmt.Println("2. 🧠 智能匹配规则根据用户行为自动调整")
	fmt.Println("3. 📊 系统性能参数基于反馈动态优化")
	fmt.Println("4. 🚀 新的用户需求模式被发现和学习")
	fmt.Println("5. 🔄 整个过程持续进行，系统不断进化")

	fmt.Println("\n🎉 FAQ系统智能学习进化演示完成！")
}

func showThinkingPatterns(db *sql.DB) {
	query := `
		SELECT pattern_type, pattern_name, user_segment, thinking_style, 
		       success_rate, usage_count
		FROM user_thinking_patterns 
		ORDER BY success_rate DESC, usage_count DESC 
		LIMIT 3
	`

	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询思维模式失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("🧠 发现的用户思维模式:")
	count := 0
	for rows.Next() {
		var patternType, patternName, userSegment, thinkingStyle string
		var successRate float32
		var usageCount int

		err := rows.Scan(&patternType, &patternName, &userSegment, &thinkingStyle, &successRate, &usageCount)
		if err != nil {
			continue
		}

		count++
		fmt.Printf("  %d. [%s] %s\n", count, patternType, patternName)
		fmt.Printf("     用户类型: %s, 思维风格: %s\n", userSegment, thinkingStyle)
		fmt.Printf("     成功率: %.1f%%, 使用次数: %d\n", successRate*100, usageCount)
	}

	if count == 0 {
		fmt.Println("  暂无发现的思维模式")
	}
}

func showEvolutionHistory(db *sql.DB) {
	query := `
		SELECT evolution_type, description, improvement_rate, applied_at
		FROM system_evolution 
		ORDER BY applied_at DESC 
		LIMIT 3
	`

	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询进化历史失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("📈 系统进化历史:")
	count := 0
	for rows.Next() {
		var evolutionType, description string
		var improvementRate float32
		var appliedAt time.Time

		err := rows.Scan(&evolutionType, &description, &improvementRate, &appliedAt)
		if err != nil {
			continue
		}

		count++
		fmt.Printf("  %d. [%s] %s\n", count, evolutionType, description)
		fmt.Printf("     改进率: %.2f%%, 时间: %s\n",
			improvementRate*100, appliedAt.Format("2006-01-02 15:04:05"))
	}

	if count == 0 {
		fmt.Println("  暂无进化历史记录")
	}
}
