package vectorstore

import (
	"database/sql"
	"encoding/binary"
	"errors"
	"faq-system/internal/embedding"
	"faq-system/internal/logger"
	"faq-system/internal/mysql"
	"fmt"
	"math"
	"sort"
	"sync"

	_ "github.com/mattn/go-sqlite3"
)

// VectorStore 向量存储 - 与原系统保持一致的结构
type VectorStore struct {
	db   *sql.DB
	lock sync.Mutex
}

// SearchResult 搜索结果
type SearchResult struct {
	ID      int     `json:"id"`
	Score   float32 `json:"score"`
	Content string  `json:"content,omitempty"`
}

// NewVectorStore 创建向量存储 - 保持与原系统一致的接口名称
func NewVectorStore(dbPath string) (*VectorStore, error) {
	return NewSQLiteStore(dbPath)
}

// NewSQLiteStore 创建SQLite向量存储 - 与原系统保持一致
func NewSQLiteStore(dbPath string) (*VectorStore, error) {
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}

	// 创建表结构（如果不存在） - 与原系统保持一致
	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS vectors (
		id INTEGER PRIMARY KEY,
		content TEXT,
		embedding BLOB
	)`)
	if err != nil {
		return nil, err
	}

	return &VectorStore{db: db}, nil
}

// float32SliceToBytes 将float32切片转换为字节 - 与原系统保持一致
func float32SliceToBytes(floats []float32) []byte {
	buf := make([]byte, 4*len(floats))
	for i, f := range floats {
		binary.LittleEndian.PutUint32(buf[i*4:], math.Float32bits(f))
	}
	return buf
}

// bytesToFloat32Slice 将字节转换为float32切片 - 与原系统保持一致
func bytesToFloat32Slice(data []byte) ([]float32, error) {
	if len(data)%4 != 0 {
		return nil, errors.New("invalid byte length for float32 slice")
	}
	floats := make([]float32, len(data)/4)
	for i := range floats {
		floats[i] = math.Float32frombits(binary.LittleEndian.Uint32(data[i*4:]))
	}
	return floats, nil
}

// UpsertVector 插入或更新向量 - 与原系统保持一致
func (vs *VectorStore) UpsertVector(id int, content string, embedding []float32) error {
	vs.lock.Lock()
	defer vs.lock.Unlock()

	embeddingBytes := float32SliceToBytes(embedding)
	_, err := vs.db.Exec(`INSERT OR REPLACE INTO vectors (id, content, embedding) VALUES (?, ?, ?)`,
		id, content, embeddingBytes)
	return err
}

// SearchTopK 搜索最相似的K个向量 - 与原系统保持一致
func (vs *VectorStore) SearchTopK(queryEmbedding []float32, k int) ([]SearchResult, error) {
	vs.lock.Lock()
	defer vs.lock.Unlock()

	rows, err := vs.db.Query(`SELECT id, content, embedding FROM vectors`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []SearchResult
	for rows.Next() {
		var id int
		var content string
		var embeddingBytes []byte

		if err := rows.Scan(&id, &content, &embeddingBytes); err != nil {
			continue
		}

		embedding, err := bytesToFloat32Slice(embeddingBytes)
		if err != nil {
			continue
		}

		similarity := cosineSimilarity(queryEmbedding, embedding)
		results = append(results, SearchResult{
			ID:      id,
			Score:   similarity,
			Content: content,
		})
	}

	// 按相似度排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	// 返回前K个结果
	if len(results) > k {
		results = results[:k]
	}

	return results, nil
}

// cosineSimilarity 计算余弦相似度 - 与原系统保持一致
func cosineSimilarity(a, b []float32) float32 {
	if len(a) != len(b) {
		return 0
	}

	var dotProduct, normA, normB float32
	for i := range a {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0
	}

	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}

// GenerateEmbeddings 为FAQ生成嵌入向量
func (vs *VectorStore) GenerateEmbeddings(faqs []mysql.FAQ, embedClient *embedding.Client) error {
	logger.Info("开始为FAQ条目生成嵌入向量...")

	for _, faq := range faqs {
		vec, err := embedClient.EmbedText(faq.Question)
		if err != nil {
			logger.Warnf("为FAQ ID=%d生成嵌入失败: %v", faq.ID, err)
			continue
		}

		if err := vs.UpsertVector(faq.ID, faq.Question, vec); err != nil {
			logger.Warnf("存储FAQ ID=%d的向量失败: %v", faq.ID, err)
		}
	}

	logger.Info("嵌入向量生成完成")
	return nil
}

// TestConnection 测试向量存储连接
func (vs *VectorStore) TestConnection() error {
	if vs.db == nil {
		return fmt.Errorf("database connection is nil")
	}
	return vs.db.Ping()
}

// Close 关闭向量存储
func (vs *VectorStore) Close() error {
	if vs.db != nil {
		return vs.db.Close()
	}
	return nil
}
