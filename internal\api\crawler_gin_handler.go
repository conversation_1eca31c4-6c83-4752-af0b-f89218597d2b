package api

import (
	"net/http"
	"strconv"

	"faq-system/internal/crawler"

	"github.com/gin-gonic/gin"
)

// CrawlerGinHandler 爬虫Gin API处理器
type CrawlerGinHandler struct {
	crawler *crawler.KnowledgeCrawler
}

// NewCrawlerGinHandler 创建爬虫Gin API处理器
func NewCrawlerGinHandler(c *crawler.KnowledgeCrawler) *CrawlerGinHandler {
	return &CrawlerGinHandler{
		crawler: c,
	}
}

// RegisterRoutes 注册爬虫相关路由
func (h *CrawlerGinHandler) RegisterRoutes(router *gin.RouterGroup) {
	// 爬虫管理
	router.POST("/start", h.StartCrawler)
	router.POST("/stop", h.StopCrawler)
	router.GET("/status", h.GetCrawlerStatus)

	// 爬取目标管理
	router.GET("/targets", h.GetTargets)
	router.POST("/targets", h.AddTarget)
	router.GET("/targets/:id", h.GetTarget)
	router.PUT("/targets/:id", h.UpdateTarget)
	router.DELETE("/targets/:id", h.DeleteTarget)
	router.POST("/targets/:id/crawl", h.ManualCrawl)

	// 爬取结果查看
	router.GET("/results", h.GetResults)
	router.GET("/results/:id", h.GetResult)

	// 爬取统计
	router.GET("/statistics", h.GetStatistics)
	router.GET("/logs", h.GetLogs)
}

// StartCrawler 启动爬虫
func (h *CrawlerGinHandler) StartCrawler(c *gin.Context) {
	if err := h.crawler.Start(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "爬虫启动成功",
	})
}

// StopCrawler 停止爬虫
func (h *CrawlerGinHandler) StopCrawler(c *gin.Context) {
	h.crawler.Stop()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "爬虫停止成功",
	})
}

// GetCrawlerStatus 获取爬虫状态
func (h *CrawlerGinHandler) GetCrawlerStatus(c *gin.Context) {
	targets := h.crawler.GetTargets()
	activeCrawls := h.crawler.GetActiveCrawls()

	activeTargets := 0
	for _, target := range targets {
		if target.Enabled {
			activeTargets++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"running":        h.crawler.IsRunning(),
			"total_targets":  len(targets),
			"active_targets": activeTargets,
			"crawling_now":   len(activeCrawls),
			"active_crawls":  activeCrawls,
		},
	})
}

// GetTargets 获取所有爬取目标
func (h *CrawlerGinHandler) GetTargets(c *gin.Context) {
	targets := h.crawler.GetTargets()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    targets,
		"total":   len(targets),
	})
}

// AddTarget 添加爬取目标
func (h *CrawlerGinHandler) AddTarget(c *gin.Context) {
	var target crawler.CrawlTarget
	if err := c.ShouldBindJSON(&target); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的JSON数据",
		})
		return
	}

	if err := h.crawler.AddTarget(&target); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "爬取目标添加成功",
		"data":    target,
	})
}

// GetTarget 获取单个爬取目标
func (h *CrawlerGinHandler) GetTarget(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的ID",
		})
		return
	}

	targets := h.crawler.GetTargets()
	for _, target := range targets {
		if target.ID == id {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    target,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success": false,
		"message": "目标不存在",
	})
}

// UpdateTarget 更新爬取目标
func (h *CrawlerGinHandler) UpdateTarget(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的ID",
		})
		return
	}

	var target crawler.CrawlTarget
	if err := c.ShouldBindJSON(&target); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的JSON数据",
		})
		return
	}

	target.ID = id
	if err := h.crawler.UpdateTarget(&target); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "爬取目标更新成功",
		"data":    target,
	})
}

// DeleteTarget 删除爬取目标
func (h *CrawlerGinHandler) DeleteTarget(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的ID",
		})
		return
	}

	if err := h.crawler.RemoveTarget(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "爬取目标删除成功",
	})
}

// ManualCrawl 手动触发爬取
func (h *CrawlerGinHandler) ManualCrawl(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的ID",
		})
		return
	}

	if err := h.crawler.ManualCrawl(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "手动爬取任务已启动",
	})
}

// GetResults 获取爬取结果
func (h *CrawlerGinHandler) GetResults(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    []interface{}{},
		"total":   0,
	})
}

// GetResult 获取单个爬取结果
func (h *CrawlerGinHandler) GetResult(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的ID",
		})
		return
	}

	_ = id

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    nil,
	})
}

// GetStatistics 获取爬取统计
func (h *CrawlerGinHandler) GetStatistics(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total_targets":     len(h.crawler.GetTargets()),
			"active_targets":    0,
			"total_crawls":      0,
			"successful_crawls": 0,
			"failed_crawls":     0,
		},
	})
}

// GetLogs 获取爬取日志
func (h *CrawlerGinHandler) GetLogs(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    []interface{}{},
		"total":   0,
	})
}
