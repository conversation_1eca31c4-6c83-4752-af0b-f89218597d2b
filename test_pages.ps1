Write-Host "Testing FAQ System Pages..." -ForegroundColor Green

# Test main page
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081/" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Main page - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Main page failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test health check
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Health check - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Health check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test learning dashboard
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081/learning_dashboard.html" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Learning dashboard - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Learning dashboard failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test crawler dashboard
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081/crawler_dashboard.html" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Crawler dashboard - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Crawler dashboard failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test ask API
try {
    $body = '{"question":"What is Go language?"}'
    $response = Invoke-WebRequest -Uri "http://localhost:8081/ask" -Method POST -ContentType "application/json" -Body $body -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ Ask API - Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Ask API failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nAll tests completed!" -ForegroundColor Yellow
Write-Host "Access URLs:" -ForegroundColor Cyan
Write-Host "- Main page: http://localhost:8081/" -ForegroundColor White
Write-Host "- Learning dashboard: http://localhost:8081/learning_dashboard.html" -ForegroundColor White
Write-Host "- Crawler dashboard: http://localhost:8081/crawler_dashboard.html" -ForegroundColor White
