<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2167 1210" style="max-width: 2167px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee"><style>#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .error-icon{fill:#a44141;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .error-text{fill:#ddd;stroke:#ddd;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .edge-thickness-normal{stroke-width:1px;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .edge-thickness-thick{stroke-width:3.5px;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .edge-pattern-solid{stroke-dasharray:0;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .marker.cross{stroke:lightgrey;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee p{margin:0;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .cluster-label text{fill:#F9FFFE;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .cluster-label span{color:#F9FFFE;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .cluster-label span p{background-color:transparent;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .label text,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee span{fill:#ccc;color:#ccc;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .node rect,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .node circle,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .node ellipse,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .node polygon,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .rough-node .label text,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .node .label text,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .image-shape .label,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .icon-shape .label{text-anchor:middle;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .rough-node .label,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .node .label,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .image-shape .label,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .icon-shape .label{text-align:center;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .node.clickable{cursor:pointer;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .arrowheadPath{fill:lightgrey;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .cluster text{fill:#F9FFFE;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .cluster span{color:#F9FFFE;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee rect.text{fill:none;stroke-width:0;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .icon-shape,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .icon-shape p,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .icon-shape rect,#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="智能规则" class="cluster"><rect height="104" width="1497.5" y="1098" x="661.5" style=""></rect><g transform="translate(1378.25, 1098)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>智能规则</p></span></div></foreignObject></g></g><g data-look="classic" id="学习数据" class="cluster"><rect height="104" width="716" y="736" x="8" style=""></rect><g transform="translate(334, 736)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>学习数据</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M1102.183,62L1106.069,66.167C1109.955,70.333,1117.728,78.667,1121.614,86.333C1125.5,94,1125.5,101,1125.5,104.5L1125.5,108"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M1150.683,166L1154.569,170.167C1158.455,174.333,1166.228,182.667,1170.114,190.333C1174,198,1174,205,1174,208.5L1174,212"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M1236,248.602L1321.583,256.335C1407.167,264.068,1578.333,279.534,1663.917,290.767C1749.5,302,1749.5,309,1749.5,312.5L1749.5,316"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M1679.5,350.302L1507.417,358.418C1335.333,366.534,991.167,382.767,819.083,394.384C647,406,647,413,647,416.5L647,420"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M672.183,478L676.069,482.167C679.955,486.333,687.728,494.667,691.614,502.333C695.5,510,695.5,517,695.5,520.5L695.5,524"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_5" d="M478.5,666.444L548.333,673.87C618.167,681.296,757.833,696.148,827.667,707.741C897.5,719.333,897.5,727.667,897.5,735.333C897.5,743,897.5,750,897.5,753.5L897.5,757"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_6" d="M897.5,815L897.5,819.167C897.5,823.333,897.5,831.667,897.5,840C897.5,848.333,897.5,856.667,940.675,867.741C983.85,878.816,1070.2,892.632,1113.375,899.54L1156.55,906.448"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_7" d="M1222.5,944L1222.5,948.167C1222.5,952.333,1222.5,960.667,1238.116,969.924C1253.732,979.182,1284.965,989.365,1300.581,994.456L1316.197,999.547"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_D_8" d="M1444,1009.081L1478.75,1002.401C1513.5,995.721,1583,982.36,1617.75,967.014C1652.5,951.667,1652.5,934.333,1652.5,917C1652.5,899.667,1652.5,882.333,1652.5,869.5C1652.5,856.667,1652.5,848.333,1652.5,835.5C1652.5,822.667,1652.5,805.333,1652.5,788C1652.5,770.667,1652.5,753.333,1652.5,740.5C1652.5,727.667,1652.5,719.333,1652.5,706.5C1652.5,693.667,1652.5,676.333,1652.5,659C1652.5,641.667,1652.5,624.333,1652.5,607C1652.5,589.667,1652.5,572.333,1652.5,555C1652.5,537.667,1652.5,520.333,1652.5,503C1652.5,485.667,1652.5,468.333,1652.5,451C1652.5,433.667,1652.5,416.333,1659.685,403.815C1666.87,391.297,1681.239,383.593,1688.424,379.742L1695.609,375.89"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_G_9" d="M1015,39.203L897.5,47.169C780,55.136,545,71.068,427.5,87.701C310,104.333,310,121.667,310,139C310,156.333,310,173.667,310,191C310,208.333,310,225.667,310,243C310,260.333,310,277.667,310,295C310,312.333,310,329.667,310,347C310,364.333,310,381.667,310,399C310,416.333,310,433.667,310,451C310,468.333,310,485.667,310,503C310,520.333,310,537.667,310,555C310,572.333,310,589.667,317.303,602.189C324.606,614.711,339.212,622.422,346.515,626.277L353.818,630.133"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_G_10" d="M1063.5,143.053L941.25,151.044C819,159.035,574.5,175.018,452.25,191.675C330,208.333,330,225.667,330,243C330,260.333,330,277.667,330,295C330,312.333,330,329.667,330,347C330,364.333,330,381.667,330,399C330,416.333,330,433.667,330,451C330,468.333,330,485.667,330,503C330,520.333,330,537.667,330,555C330,572.333,330,589.667,335.734,602.132C341.469,614.597,352.937,622.194,358.671,625.993L364.406,629.791"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_G_11" d="M1112,246.99L987.667,254.992C863.333,262.993,614.667,278.997,490.333,295.665C366,312.333,366,329.667,366,347C366,364.333,366,381.667,366,399C366,416.333,366,433.667,366,451C366,468.333,366,485.667,366,503C366,520.333,366,537.667,366,555C366,572.333,366,589.667,368.984,601.984C371.967,614.301,377.934,621.602,380.918,625.252L383.901,628.903"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_G_12" d="M621.817,478L617.931,482.167C614.045,486.333,606.272,494.667,602.386,507.5C598.5,520.333,598.5,537.667,598.5,555C598.5,572.333,598.5,589.667,579.143,603.631C559.786,617.595,521.072,628.191,501.715,633.488L482.358,638.786"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_13" d="M695.5,582L695.5,586.167C695.5,590.333,695.5,598.667,659.989,609.267C624.479,619.868,553.457,632.736,517.947,639.17L482.436,645.604"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_I_14" d="M1111.5,815L1111.5,819.167C1111.5,823.333,1111.5,831.667,1111.5,840C1111.5,848.333,1111.5,856.667,1119.791,864.717C1128.081,872.768,1144.662,880.535,1152.953,884.419L1161.243,888.303"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_I_15" d="M1333.5,815L1333.5,819.167C1333.5,823.333,1333.5,831.667,1333.5,840C1333.5,848.333,1333.5,856.667,1325.209,864.717C1316.919,872.768,1300.338,880.535,1292.047,884.419L1283.757,888.303"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_I_16" d="M1539.5,815L1539.5,819.167C1539.5,823.333,1539.5,831.667,1539.5,840C1539.5,848.333,1539.5,856.667,1497.658,867.697C1455.816,878.727,1372.131,892.455,1330.289,899.318L1288.447,906.182"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_N_17" d="M338.5,670.993L299.583,677.661C260.667,684.329,182.833,697.664,143.917,708.499C105,719.333,105,727.667,105,735.333C105,743,105,750,105,753.5L105,757"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_O_18" d="M341.26,686L330.883,690.167C320.506,694.333,299.753,702.667,289.377,711C279,719.333,279,727.667,279,735.333C279,743,279,750,279,753.5L279,757"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_P_19" d="M431.606,686L435.171,690.167C438.737,694.333,445.869,702.667,449.434,711C453,719.333,453,727.667,453,735.333C453,743,453,750,453,753.5L453,757"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_Q_20" d="M478.5,675.659L503.25,681.549C528,687.439,577.5,699.22,602.25,709.277C627,719.333,627,727.667,627,735.333C627,743,627,750,627,753.5L627,757"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_R_21" d="M1320,1026.037L1223.667,1033.865C1127.333,1041.692,934.667,1057.346,838.333,1069.34C742,1081.333,742,1089.667,744.836,1097.474C747.671,1105.281,753.342,1112.563,756.178,1116.204L759.013,1119.844"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_S_22" d="M1382,1048L1382,1052.167C1382,1056.333,1382,1064.667,1382,1073C1382,1081.333,1382,1089.667,1385.432,1097.512C1388.863,1105.358,1395.726,1112.717,1399.158,1116.396L1402.589,1120.075"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_T_23" d="M1444,1030.238L1491.833,1037.365C1539.667,1044.492,1635.333,1058.746,1683.167,1070.04C1731,1081.333,1731,1089.667,1737.87,1097.675C1744.74,1105.683,1758.48,1113.365,1765.35,1117.207L1772.22,1121.048"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_U_24" d="M1444,1026.767L1526.833,1034.473C1609.667,1042.178,1775.333,1057.589,1858.167,1069.461C1941,1081.333,1941,1089.667,1948.185,1097.685C1955.37,1105.703,1969.739,1113.407,1976.924,1117.258L1984.109,1121.11"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_D_25" d="M787.692,1123L788.494,1118.833C789.295,1114.667,790.897,1106.333,791.699,1098C792.5,1089.667,792.5,1081.333,792.5,1068.5C792.5,1055.667,792.5,1038.333,792.5,1021C792.5,1003.667,792.5,986.333,792.5,969C792.5,951.667,792.5,934.333,792.5,917C792.5,899.667,792.5,882.333,792.5,869.5C792.5,856.667,792.5,848.333,792.5,835.5C792.5,822.667,792.5,805.333,792.5,788C792.5,770.667,792.5,753.333,792.5,740.5C792.5,727.667,792.5,719.333,792.5,706.5C792.5,693.667,792.5,676.333,792.5,659C792.5,641.667,792.5,624.333,792.5,607C792.5,589.667,792.5,572.333,792.5,555C792.5,537.667,792.5,520.333,792.5,503C792.5,485.667,792.5,468.333,792.5,451C792.5,433.667,792.5,416.333,939.668,399.67C1086.835,383.007,1381.171,367.014,1528.338,359.017L1675.506,351.021"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_D_26" d="M1508.5,1135.54L1542.25,1129.283C1576,1123.027,1643.5,1110.513,1677.25,1100.09C1711,1089.667,1711,1081.333,1711,1068.5C1711,1055.667,1711,1038.333,1711,1021C1711,1003.667,1711,986.333,1711,969C1711,951.667,1711,934.333,1711,917C1711,899.667,1711,882.333,1711,869.5C1711,856.667,1711,848.333,1711,835.5C1711,822.667,1711,805.333,1711,788C1711,770.667,1711,753.333,1711,740.5C1711,727.667,1711,719.333,1711,706.5C1711,693.667,1711,676.333,1711,659C1711,641.667,1711,624.333,1711,607C1711,589.667,1711,572.333,1711,555C1711,537.667,1711,520.333,1711,503C1711,485.667,1711,468.333,1711,451C1711,433.667,1711,416.333,1713.688,404.036C1716.376,391.738,1721.753,384.477,1724.441,380.846L1727.129,377.215"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_D_27" d="M1874.365,1123L1882.138,1118.833C1889.91,1114.667,1905.455,1106.333,1913.228,1098C1921,1089.667,1921,1081.333,1921,1068.5C1921,1055.667,1921,1038.333,1921,1021C1921,1003.667,1921,986.333,1921,969C1921,951.667,1921,934.333,1921,917C1921,899.667,1921,882.333,1921,869.5C1921,856.667,1921,848.333,1921,835.5C1921,822.667,1921,805.333,1921,788C1921,770.667,1921,753.333,1921,740.5C1921,727.667,1921,719.333,1921,706.5C1921,693.667,1921,676.333,1921,659C1921,641.667,1921,624.333,1921,607C1921,589.667,1921,572.333,1921,555C1921,537.667,1921,520.333,1921,503C1921,485.667,1921,468.333,1921,451C1921,433.667,1921,416.333,1904.721,402.731C1888.443,389.128,1855.885,379.257,1839.607,374.321L1823.328,369.385"></path><path marker-end="url(#mermaid-ed4e1ae5-0e3b-4e75-8f17-f0ef8c3f82ee_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_D_28" d="M2064.221,1123L2068.268,1118.833C2072.314,1114.667,2080.407,1106.333,2084.454,1098C2088.5,1089.667,2088.5,1081.333,2088.5,1068.5C2088.5,1055.667,2088.5,1038.333,2088.5,1021C2088.5,1003.667,2088.5,986.333,2088.5,969C2088.5,951.667,2088.5,934.333,2088.5,917C2088.5,899.667,2088.5,882.333,2088.5,869.5C2088.5,856.667,2088.5,848.333,2088.5,835.5C2088.5,822.667,2088.5,805.333,2088.5,788C2088.5,770.667,2088.5,753.333,2088.5,740.5C2088.5,727.667,2088.5,719.333,2088.5,706.5C2088.5,693.667,2088.5,676.333,2088.5,659C2088.5,641.667,2088.5,624.333,2088.5,607C2088.5,589.667,2088.5,572.333,2088.5,555C2088.5,537.667,2088.5,520.333,2088.5,503C2088.5,485.667,2088.5,468.333,2088.5,451C2088.5,433.667,2088.5,416.333,2044.326,400.891C2000.151,385.448,1911.803,371.896,1867.628,365.12L1823.454,358.344"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1077, 35)" id="flowchart-A-0" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户查询</p></span></div></foreignObject></g></g><g transform="translate(1125.5, 139)" id="flowchart-B-1" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>意图识别</p></span></div></foreignObject></g></g><g transform="translate(1174, 243)" id="flowchart-C-3" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>向量搜索</p></span></div></foreignObject></g></g><g transform="translate(1749.5, 347)" id="flowchart-D-5" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>智能匹配器</p></span></div></foreignObject></g></g><g transform="translate(647, 451)" id="flowchart-E-7" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>响应生成</p></span></div></foreignObject></g></g><g transform="translate(695.5, 555)" id="flowchart-F-9" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户反馈</p></span></div></foreignObject></g></g><g transform="translate(408.5, 659)" id="flowchart-G-10" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据收集层</p></span></div></foreignObject></g></g><g transform="translate(897.5, 788)" id="flowchart-H-11" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>学习分析层</p></span></div></foreignObject></g></g><g transform="translate(1222.5, 917)" id="flowchart-I-13" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>进化引擎</p></span></div></foreignObject></g></g><g transform="translate(1382, 1021)" id="flowchart-J-15" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>规则优化</p></span></div></foreignObject></g></g><g transform="translate(1111.5, 788)" id="flowchart-K-28" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户思维模式分析</p></span></div></foreignObject></g></g><g transform="translate(1333.5, 788)" id="flowchart-L-30" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>系统性能监控</p></span></div></foreignObject></g></g><g transform="translate(1539.5, 788)" id="flowchart-M-32" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>反馈模式识别</p></span></div></foreignObject></g></g><g transform="translate(105, 788)" id="flowchart-N-34" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询记录</p></span></div></foreignObject></g></g><g transform="translate(279, 788)" id="flowchart-O-35" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>响应记录</p></span></div></foreignObject></g></g><g transform="translate(453, 788)" id="flowchart-P-36" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户反馈</p></span></div></foreignObject></g></g><g transform="translate(627, 788)" id="flowchart-Q-37" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>行为追踪</p></span></div></foreignObject></g></g><g transform="translate(782.5, 1150)" id="flowchart-R-46" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>关键词提升规则</p></span></div></foreignObject></g></g><g transform="translate(1430.5, 1150)" id="flowchart-S-47" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>意图映射规则</p></span></div></foreignObject></g></g><g transform="translate(1824, 1150)" id="flowchart-T-48" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户偏好规则</p></span></div></foreignObject></g></g><g transform="translate(2038, 1150)" id="flowchart-U-49" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>上下文感知规则</p></span></div></foreignObject></g></g></g></g></g></svg>