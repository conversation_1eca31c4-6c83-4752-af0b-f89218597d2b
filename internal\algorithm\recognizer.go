package algorithm

import (
	"regexp"
	"strings"
	"unicode"
)

// AlgorithmType 算法类型枚举
type AlgorithmType int

const (
	Unknown AlgorithmType = iota
	MathExpression
	SortingAlgorithm
	SearchAlgorithm
	GraphAlgorithm
	DynamicProgramming
	DataStructure
	NumberTheory
	Geometry
	Statistics
	LinearAlgebra
)

// AlgorithmRequest 算法请求结构
type AlgorithmRequest struct {
	Query      string
	Type       AlgorithmType
	Expression string
	Parameters map[string]interface{}
	Confidence float32
}

// Recognizer 算法识别器
type Recognizer struct {
	mathPatterns      []*regexp.Regexp
	algorithmKeywords map[AlgorithmType][]string
}

// NewRecognizer 创建算法识别器
func NewRecognizer() *Recognizer {
	r := &Recognizer{
		algorithmKeywords: make(map[AlgorithmType][]string),
	}
	r.initializePatterns()
	r.initializeKeywords()
	return r
}

// RecognizeAlgorithm 识别算法类型和提取参数
func (r *Recognizer) RecognizeAlgorithm(query string) *AlgorithmRequest {
	query = strings.TrimSpace(query)

	// 1. 首先检查是否为数学表达式
	if mathExpr := r.extractMathExpression(query); mathExpr != "" {
		return &AlgorithmRequest{
			Query:      query,
			Type:       MathExpression,
			Expression: mathExpr,
			Parameters: make(map[string]interface{}),
			Confidence: r.calculateMathConfidence(mathExpr),
		}
	}

	// 2. 检查算法关键词
	bestType, confidence := r.classifyByKeywords(query)
	// 降低置信度阈值，提高算法识别的敏感性
	if confidence > 0.1 {
		return &AlgorithmRequest{
			Query:      query,
			Type:       bestType,
			Expression: "",
			Parameters: r.extractParameters(query, bestType),
			Confidence: confidence,
		}
	}

	return &AlgorithmRequest{
		Query:      query,
		Type:       Unknown,
		Expression: "",
		Parameters: make(map[string]interface{}),
		Confidence: 0.0,
	}
}

// initializePatterns 初始化数学表达式模式
func (r *Recognizer) initializePatterns() {
	patterns := []string{
		// 基础数学运算
		`^\s*[\d\+\-\*\/\(\)\.\s]+\s*$`,
		// 数学表达式后跟等号（如 1+1= 或 1+1=?）
		`^\s*[\d\+\-\*\/\(\)\.\s]+\s*=\s*\??\s*$`,
		// 包含函数的表达式
		`.*\b(sin|cos|tan|log|ln|sqrt|abs|exp|pow)\s*\([^)]+\).*`,
		// 方程式
		`.*[a-zA-Z]\s*=\s*.*`,
		// 包含变量的表达式
		`.*\b[a-zA-Z]\s*[\+\-\*\/]\s*\d+.*`,
		// 求导积分
		`.*\b(d\/dx|∫|integral|derivative).*`,
		// 矩阵运算
		`.*\[\s*[\d\s,\-\+\*\/]+\s*\].*`,
	}

	for _, pattern := range patterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			r.mathPatterns = append(r.mathPatterns, compiled)
		}
	}
}

// initializeKeywords 初始化算法关键词
func (r *Recognizer) initializeKeywords() {
	// 数学表达式关键词
	r.algorithmKeywords[MathExpression] = []string{
		"计算", "算", "求", "等于", "加", "减", "乘", "除", "乘法", "除法", "加法", "减法",
		"平方", "开方", "平方根", "立方", "次方", "幂", "指数", "对数", "log", "ln",
		"三角函数", "sin", "cos", "tan", "正弦", "余弦", "正切",
		"绝对值", "abs", "最大值", "最小值", "max", "min",
		"数学", "运算", "表达式", "公式", "方程", "解方程",
		"多少", "结果", "答案", "值", "数值",
	}

	r.algorithmKeywords[SortingAlgorithm] = []string{
		"排序", "sort", "冒泡排序", "快速排序", "归并排序", "堆排序",
		"插入排序", "选择排序", "bubble", "quick", "merge", "heap",
		"insertion", "selection", "排列", "从小到大", "从大到小",
	}

	r.algorithmKeywords[SearchAlgorithm] = []string{
		"搜索", "查找", "search", "find", "二分查找", "线性搜索",
		"binary search", "linear search", "深度优先", "广度优先",
		"DFS", "BFS", "查询", "定位",
	}

	r.algorithmKeywords[GraphAlgorithm] = []string{
		"图", "graph", "最短路径", "dijkstra", "floyd", "拓扑排序",
		"连通性", "生成树", "最小生成树", "网络", "节点", "边",
		"路径", "遍历", "邻接", "度数",
	}

	r.algorithmKeywords[DynamicProgramming] = []string{
		"动态规划", "dynamic programming", "dp", "递推", "最优化",
		"背包问题", "斐波那契", "最长公共子序列", "编辑距离",
		"状态转移", "记忆化", "memoization",
	}

	r.algorithmKeywords[DataStructure] = []string{
		"数据结构", "data structure", "栈", "队列", "链表", "树",
		"stack", "queue", "list", "tree", "哈希表", "hash",
		"数组", "array", "堆", "heap", "图", "集合", "set",
	}

	r.algorithmKeywords[NumberTheory] = []string{
		"数论", "质数", "素数", "prime", "最大公约数", "最小公倍数",
		"gcd", "lcm", "模运算", "同余", "欧几里得", "费马",
	}

	r.algorithmKeywords[Geometry] = []string{
		"几何", "geometry", "点", "线", "面", "角度", "距离",
		"坐标", "向量", "圆", "三角形", "多边形", "面积", "周长",
	}

	r.algorithmKeywords[Statistics] = []string{
		"统计", "statistics", "平均数", "中位数", "众数", "方差",
		"标准差", "概率", "probability", "分布", "回归", "相关性",
	}

	r.algorithmKeywords[LinearAlgebra] = []string{
		"线性代数", "矩阵", "matrix", "向量", "vector", "特征值",
		"特征向量", "行列式", "determinant", "逆矩阵", "转置",
	}
}

// extractMathExpression 提取数学表达式
func (r *Recognizer) extractMathExpression(query string) string {
	// 移除中文描述，保留数学部分
	query = strings.ToLower(query)

	// 查找数学表达式模式
	for _, pattern := range r.mathPatterns {
		if pattern.MatchString(query) {
			// 提取纯数学部分
			return r.cleanMathExpression(query)
		}
	}

	// 检查是否包含数字和运算符
	hasNumber := false
	hasOperator := false

	for _, char := range query {
		if unicode.IsDigit(char) {
			hasNumber = true
		}
		if strings.ContainsRune("+-*/=", char) {
			hasOperator = true
		}
	}

	if hasNumber && hasOperator {
		return r.cleanMathExpression(query)
	}

	return ""
}

// cleanMathExpression 清理数学表达式
func (r *Recognizer) cleanMathExpression(expr string) string {
	// 首先尝试使用正则表达式直接提取数学表达式
	if mathExpr := r.extractPureMathExpression(expr); mathExpr != "" {
		return mathExpr
	}

	// 移除常见的中文描述词
	removeWords := []string{
		"帮我", "请", "帮", "我", "计算", "算", "求", "一下", "多少", "结果", "答案", "是", "的", "什么", "等于",
		"calculate", "compute", "what", "is", "equals", "help", "please",
	}

	cleaned := expr
	for _, word := range removeWords {
		cleaned = strings.ReplaceAll(cleaned, word, "")
	}

	// 处理冒号分隔符
	if strings.Contains(cleaned, "：") {
		parts := strings.Split(cleaned, "：")
		if len(parts) > 1 {
			cleaned = parts[len(parts)-1] // 取冒号后面的部分
		}
	}
	if strings.Contains(cleaned, ":") {
		parts := strings.Split(cleaned, ":")
		if len(parts) > 1 {
			cleaned = parts[len(parts)-1] // 取冒号后面的部分
		}
	}

	// 处理等号情况：如果表达式以 = 或 =? 结尾，提取等号前面的部分
	if strings.HasSuffix(cleaned, "=?") {
		cleaned = strings.TrimSuffix(cleaned, "=?")
	} else if strings.HasSuffix(cleaned, "=") {
		cleaned = strings.TrimSuffix(cleaned, "=")
	}

	// 处理问号
	cleaned = strings.ReplaceAll(cleaned, "?", "")

	// 移除多余空格
	cleaned = regexp.MustCompile(`\s+`).ReplaceAllString(cleaned, " ")
	cleaned = strings.TrimSpace(cleaned)

	return cleaned
}

// extractPureMathExpression 使用正则表达式直接提取纯数学表达式
func (r *Recognizer) extractPureMathExpression(text string) string {
	// 匹配数学表达式模式：数字、运算符、括号、小数点、空格
	patterns := []string{
		`\d+(?:\.\d+)?\s*[\+\-\*\/]\s*\d+(?:\.\d+)?(?:\s*[\+\-\*\/]\s*\d+(?:\.\d+)?)*`, // 基本四则运算
		`\d+(?:\.\d+)?\s*[\+\-\*\/]\s*\d+(?:\.\d+)?`,                                   // 简单两数运算
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		if match := re.FindString(text); match != "" {
			return strings.TrimSpace(match)
		}
	}

	return ""
}

// classifyByKeywords 基于关键词分类
func (r *Recognizer) classifyByKeywords(query string) (AlgorithmType, float32) {
	query = strings.ToLower(query)
	bestType := Unknown
	maxScore := float32(0)

	for algType, keywords := range r.algorithmKeywords {
		score := float32(0)
		for _, keyword := range keywords {
			if strings.Contains(query, keyword) {
				// 完全匹配得分更高
				if query == keyword {
					score += 2.0
				} else {
					score += 1.0
				}
			}
		}

		// 计算相对分数
		relativeScore := score / float32(len(keywords))

		// 给数学表达式类型额外权重
		if algType == MathExpression && score > 0 {
			relativeScore *= 1.5 // 增加50%权重
		}

		if relativeScore > maxScore {
			maxScore = relativeScore
			bestType = algType
		}
	}

	return bestType, maxScore
}

// calculateMathConfidence 计算数学表达式置信度
func (r *Recognizer) calculateMathConfidence(expr string) float32 {
	if expr == "" {
		return 0.0
	}

	confidence := float32(0.5) // 基础分数

	// 包含数字
	if regexp.MustCompile(`\d`).MatchString(expr) {
		confidence += 0.2
	}

	// 包含运算符
	if regexp.MustCompile(`[\+\-\*\/]`).MatchString(expr) {
		confidence += 0.2
	}

	// 包含函数
	if regexp.MustCompile(`\b(sin|cos|tan|log|sqrt)\b`).MatchString(expr) {
		confidence += 0.1
	}

	// 包含括号
	if strings.Contains(expr, "(") && strings.Contains(expr, ")") {
		confidence += 0.1
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// extractParameters 提取算法参数
func (r *Recognizer) extractParameters(query string, algType AlgorithmType) map[string]interface{} {
	params := make(map[string]interface{})

	switch algType {
	case SortingAlgorithm:
		// 提取排序相关参数
		if strings.Contains(query, "从小到大") || strings.Contains(query, "升序") {
			params["order"] = "asc"
		} else if strings.Contains(query, "从大到小") || strings.Contains(query, "降序") {
			params["order"] = "desc"
		}

		// 提取数组
		if numbers := r.extractNumbers(query); len(numbers) > 0 {
			params["array"] = numbers
		}

	case SearchAlgorithm:
		// 提取搜索目标
		if numbers := r.extractNumbers(query); len(numbers) > 0 {
			if len(numbers) == 1 {
				params["target"] = numbers[0]
			} else {
				params["array"] = numbers
			}
		}
	}

	return params
}

// extractNumbers 从文本中提取数字
func (r *Recognizer) extractNumbers(text string) []int {
	re := regexp.MustCompile(`-?\d+`)
	matches := re.FindAllString(text, -1)

	var numbers []int
	for _, match := range matches {
		if num := parseInt(match); num != nil {
			numbers = append(numbers, *num)
		}
	}

	return numbers
}

// parseInt 安全的字符串转整数
func parseInt(s string) *int {
	var result int
	var sign int = 1
	var i int = 0

	if len(s) == 0 {
		return nil
	}

	if s[0] == '-' {
		sign = -1
		i = 1
	} else if s[0] == '+' {
		i = 1
	}

	for i < len(s) {
		if s[i] < '0' || s[i] > '9' {
			return nil
		}
		result = result*10 + int(s[i]-'0')
		i++
	}

	result *= sign
	return &result
}

// GetAlgorithmTypeName 获取算法类型名称
func (r *Recognizer) GetAlgorithmTypeName(algType AlgorithmType) string {
	switch algType {
	case MathExpression:
		return "数学表达式"
	case SortingAlgorithm:
		return "排序算法"
	case SearchAlgorithm:
		return "搜索算法"
	case GraphAlgorithm:
		return "图算法"
	case DynamicProgramming:
		return "动态规划"
	case DataStructure:
		return "数据结构"
	case NumberTheory:
		return "数论"
	case Geometry:
		return "几何"
	case Statistics:
		return "统计学"
	case LinearAlgebra:
		return "线性代数"
	default:
		return "未知类型"
	}
}
