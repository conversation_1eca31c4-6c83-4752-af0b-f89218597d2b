package main

import (
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	fmt.Println("🔍 调试C#问题...")

	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 1. 检查learned_knowledge表中是否有问题数据
	fmt.Println("📊 检查learned_knowledge表中的C#相关数据...")
	query := `
		SELECT id, question, answer, source, confidence, status, created_at
		FROM learned_knowledge 
		WHERE question LIKE '%C#%' OR answer LIKE '%C#%' OR question LIKE '%c#%' OR answer LIKE '%c#%'
		ORDER BY created_at DESC
	`
	
	rows, err := db.Query(query)
	if err != nil {
		log.Fatalf("查询学习知识失败: %v", err)
	}
	defer rows.Close()

	fmt.Println("ID\t问题\t\t\t答案\t\t\t来源\t置信度\t状态\t创建时间")
	fmt.Println("---\t---\t\t\t---\t\t\t---\t---\t---\t---")
	
	foundProblematic := false
	for rows.Next() {
		var id int
		var question, answer, source, status, createdAt string
		var confidence float64
		
		err := rows.Scan(&id, &question, &answer, &source, &confidence, &status, &createdAt)
		if err != nil {
			log.Printf("扫描行失败: %v", err)
			continue
		}
		
		// 检查是否是问题数据
		isProblematic := question == answer || 
			(question == "C#是什么语言" && answer == "C#是什么语言。") ||
			(question == "C#是什么" && answer == "C#是什么。")
		
		if isProblematic {
			foundProblematic = true
			fmt.Printf("🚨 %d\t%s\t%s\t%s\t%.2f\t%s\t%s\n", 
				id, question, answer, source, confidence, status, createdAt)
		} else {
			fmt.Printf("✅ %d\t%s\t%s\t%s\t%.2f\t%s\t%s\n", 
				id, question, answer, source, confidence, status, createdAt)
		}
	}

	if foundProblematic {
		fmt.Println("\n🚨 发现问题数据！需要清理...")
		
		// 删除问题数据
		deleteQuery := `
			DELETE FROM learned_knowledge 
			WHERE question = answer 
			OR (question LIKE '%C#是什么%' AND answer LIKE '%C#是什么%')
			OR (question LIKE '%c#是什么%' AND answer LIKE '%c#是什么%')
		`
		
		result, err := db.Exec(deleteQuery)
		if err != nil {
			fmt.Printf("❌ 删除问题数据失败: %v\n", err)
		} else {
			affected, _ := result.RowsAffected()
			fmt.Printf("✅ 已删除 %d 条问题数据\n", affected)
		}
	} else {
		fmt.Println("\n✅ 未发现问题数据")
	}

	// 2. 检查FAQ表中是否有C#相关数据
	fmt.Println("\n📊 检查FAQ表中的C#相关数据...")
	faqQuery := `
		SELECT id, question, answer 
		FROM faq 
		WHERE question LIKE '%C#%' OR answer LIKE '%C#%' OR question LIKE '%c#%' OR answer LIKE '%c#%'
		ORDER BY id
	`
	
	faqRows, err := db.Query(faqQuery)
	if err != nil {
		log.Printf("查询FAQ失败: %v", err)
	} else {
		defer faqRows.Close()
		
		fmt.Println("ID\t问题\t\t\t答案")
		fmt.Println("---\t---\t\t\t---")
		
		faqFound := false
		for faqRows.Next() {
			var id int
			var question, answer string
			
			err := faqRows.Scan(&id, &question, &answer)
			if err != nil {
				continue
			}
			
			faqFound = true
			// 截断长文本
			if len(question) > 30 {
				question = question[:30] + "..."
			}
			if len(answer) > 50 {
				answer = answer[:50] + "..."
			}
			
			fmt.Printf("%d\t%s\t%s\n", id, question, answer)
		}
		
		if !faqFound {
			fmt.Println("未找到C#相关的FAQ数据")
		}
	}

	// 3. 插入正确的C#知识
	fmt.Println("\n📝 插入正确的C#知识...")
	insertQuery := `
		INSERT IGNORE INTO learned_knowledge 
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES 
		('什么是C#？', 'C#是微软开发的一种现代面向对象编程语言，运行在.NET框架上。', 'system', 0.95, 'technology', '["c#", "编程语言", "微软", ".net", "面向对象"]', '系统预置知识', 'system', 'approved'),
		('C#是什么？', 'C#是微软开发的一种现代面向对象编程语言，具有强类型、内存管理等特点。', 'system', 0.95, 'technology', '["c#", "编程语言", "微软"]', '系统预置知识', 'system', 'approved')
	`
	
	result, err := db.Exec(insertQuery)
	if err != nil {
		fmt.Printf("❌ 插入C#知识失败: %v\n", err)
	} else {
		affected, _ := result.RowsAffected()
		fmt.Printf("✅ 插入了 %d 条C#知识\n", affected)
	}

	fmt.Println("\n🎉 调试完成！请重新测试C#问题。")
}
