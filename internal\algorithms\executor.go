package algorithms

import (
	"fmt"
	"strings"

	"faq-system/internal/algorithm"
	"faq-system/internal/math"
)

// ExecutionResult 算法执行结果
type ExecutionResult struct {
	Type        algorithm.AlgorithmType
	Success     bool
	Result      interface{}
	Explanation string
	Steps       []string
	Error       string
}

// AlgorithmExecutor 算法执行器
type AlgorithmExecutor struct {
	calculator      *math.Calculator
	sortingEngine   *SortingEngine
	searchingEngine *SearchingEngine
}

// NewAlgorithmExecutor 创建算法执行器
func NewAlgorithmExecutor() *AlgorithmExecutor {
	return &AlgorithmExecutor{
		calculator:      math.NewCalculator(),
		sortingEngine:   NewSortingEngine(),
		searchingEngine: NewSearchingEngine(),
	}
}

// Execute 执行算法
func (ae *AlgorithmExecutor) Execute(request *algorithm.AlgorithmRequest) *ExecutionResult {
	switch request.Type {
	case algorithm.MathExpression:
		return ae.executeMathExpression(request)
	case algorithm.SortingAlgorithm:
		return ae.executeSortingAlgorithm(request)
	case algorithm.SearchAlgorithm:
		return ae.executeSearchAlgorithm(request)
	case algorithm.DataStructure:
		return ae.executeDataStructure(request)
	case algorithm.NumberTheory:
		return ae.executeNumberTheory(request)
	case algorithm.Statistics:
		return ae.executeStatistics(request)
	default:
		return &ExecutionResult{
			Type:    request.Type,
			Success: false,
			Error:   "不支持的算法类型",
		}
	}
}

// executeMathExpression 执行数学表达式
func (ae *AlgorithmExecutor) executeMathExpression(request *algorithm.AlgorithmRequest) *ExecutionResult {
	result, steps, err := ae.calculator.CalculateWithSteps(request.Expression)

	if err != nil {
		return &ExecutionResult{
			Type:    algorithm.MathExpression,
			Success: false,
			Error:   err.Error(),
		}
	}

	explanation := fmt.Sprintf("🧮 **数学计算结果**\n\n")
	explanation += fmt.Sprintf("**表达式**: `%s`\n", request.Expression)
	explanation += fmt.Sprintf("**结果**: `%s`\n\n", ae.calculator.FormatResult(result))

	if len(steps) > 1 {
		explanation += "**计算步骤**:\n"
		for i, step := range steps {
			explanation += fmt.Sprintf("%d. %s\n", i+1, step)
		}
	}

	return &ExecutionResult{
		Type:        algorithm.MathExpression,
		Success:     true,
		Result:      result,
		Explanation: explanation,
		Steps:       steps,
	}
}

// executeSortingAlgorithm 执行排序算法
func (ae *AlgorithmExecutor) executeSortingAlgorithm(request *algorithm.AlgorithmRequest) *ExecutionResult {
	// 从参数中获取数组
	var data []int
	if arr, exists := request.Parameters["array"]; exists {
		if intArr, ok := arr.([]int); ok {
			data = intArr
		}
	}

	if len(data) == 0 {
		// 使用默认示例数组
		data = []int{64, 34, 25, 12, 22, 11, 90}
	}

	// 确定排序算法
	sortAlgorithm := "quick" // 默认快速排序
	query := strings.ToLower(request.Query)
	if strings.Contains(query, "冒泡") || strings.Contains(query, "bubble") {
		sortAlgorithm = "bubble"
	} else if strings.Contains(query, "归并") || strings.Contains(query, "merge") {
		sortAlgorithm = "merge"
	} else if strings.Contains(query, "插入") || strings.Contains(query, "insertion") {
		sortAlgorithm = "insertion"
	} else if strings.Contains(query, "选择") || strings.Contains(query, "selection") {
		sortAlgorithm = "selection"
	}

	sortResult := ae.sortingEngine.ExecuteSort(sortAlgorithm, data)

	explanation := fmt.Sprintf("🔄 **%s 演示**\n\n", sortResult.Algorithm)
	explanation += fmt.Sprintf("**原始数组**: %v\n", sortResult.OriginalData)
	explanation += fmt.Sprintf("**排序结果**: %v\n", sortResult.SortedData)
	explanation += fmt.Sprintf("**比较次数**: %d\n", sortResult.Comparisons)
	explanation += fmt.Sprintf("**交换次数**: %d\n", sortResult.Swaps)
	explanation += fmt.Sprintf("**时间复杂度**: %s\n", sortResult.TimeComplexity)
	explanation += fmt.Sprintf("**空间复杂度**: %s\n\n", sortResult.SpaceComplexity)

	if len(sortResult.Steps) > 0 {
		explanation += "**详细步骤**:\n"
		for i, step := range sortResult.Steps {
			if i < 20 { // 限制显示步骤数量
				explanation += fmt.Sprintf("%s\n", step)
			}
		}
		if len(sortResult.Steps) > 20 {
			explanation += fmt.Sprintf("... (共 %d 步，仅显示前 20 步)\n", len(sortResult.Steps))
		}
	}

	return &ExecutionResult{
		Type:        algorithm.SortingAlgorithm,
		Success:     true,
		Result:      sortResult,
		Explanation: explanation,
		Steps:       sortResult.Steps,
	}
}

// executeSearchAlgorithm 执行搜索算法
func (ae *AlgorithmExecutor) executeSearchAlgorithm(request *algorithm.AlgorithmRequest) *ExecutionResult {
	// 从参数中获取数组和目标值
	var data []int
	var target int

	if arr, exists := request.Parameters["array"]; exists {
		if intArr, ok := arr.([]int); ok {
			data = intArr
		}
	}

	if t, exists := request.Parameters["target"]; exists {
		if intTarget, ok := t.(int); ok {
			target = intTarget
		}
	}

	if len(data) == 0 {
		// 使用默认示例数组
		data = []int{2, 5, 8, 12, 16, 23, 38, 45, 56, 67, 78}
	}

	if target == 0 {
		// 使用默认目标值
		target = 23
	}

	// 确定搜索算法
	searchAlgorithm := "binary" // 默认二分搜索
	query := strings.ToLower(request.Query)
	if strings.Contains(query, "线性") || strings.Contains(query, "linear") || strings.Contains(query, "顺序") {
		searchAlgorithm = "linear"
	} else if strings.Contains(query, "插值") || strings.Contains(query, "interpolation") {
		searchAlgorithm = "interpolation"
	} else if strings.Contains(query, "指数") || strings.Contains(query, "exponential") {
		searchAlgorithm = "exponential"
	}

	searchResult := ae.searchingEngine.ExecuteSearch(searchAlgorithm, data, target)

	explanation := fmt.Sprintf("🔍 **%s 演示**\n\n", searchResult.Algorithm)
	explanation += fmt.Sprintf("**数组**: %v\n", searchResult.Data)
	explanation += fmt.Sprintf("**目标值**: %d\n", searchResult.Target)
	explanation += fmt.Sprintf("**搜索结果**: %s\n", func() string {
		if searchResult.Found {
			return fmt.Sprintf("找到，位置: %d", searchResult.Index)
		}
		return "未找到"
	}())
	explanation += fmt.Sprintf("**比较次数**: %d\n", searchResult.Comparisons)
	explanation += fmt.Sprintf("**时间复杂度**: %s\n", searchResult.TimeComplexity)
	explanation += fmt.Sprintf("**空间复杂度**: %s\n\n", searchResult.SpaceComplexity)

	if len(searchResult.Steps) > 0 {
		explanation += "**搜索步骤**:\n"
		for _, step := range searchResult.Steps {
			explanation += fmt.Sprintf("%s\n", step)
		}
	}

	return &ExecutionResult{
		Type:        algorithm.SearchAlgorithm,
		Success:     true,
		Result:      searchResult,
		Explanation: explanation,
		Steps:       searchResult.Steps,
	}
}

// executeDataStructure 执行数据结构操作
func (ae *AlgorithmExecutor) executeDataStructure(request *algorithm.AlgorithmRequest) *ExecutionResult {
	query := strings.ToLower(request.Query)

	explanation := "📊 **数据结构知识**\n\n"

	if strings.Contains(query, "栈") || strings.Contains(query, "stack") {
		explanation += "**栈 (Stack)**\n"
		explanation += "- 后进先出 (LIFO) 的数据结构\n"
		explanation += "- 主要操作：push(入栈)、pop(出栈)、top(查看栈顶)\n"
		explanation += "- 应用：函数调用、表达式求值、括号匹配\n"
		explanation += "- 时间复杂度：O(1) for all operations\n"
	} else if strings.Contains(query, "队列") || strings.Contains(query, "queue") {
		explanation += "**队列 (Queue)**\n"
		explanation += "- 先进先出 (FIFO) 的数据结构\n"
		explanation += "- 主要操作：enqueue(入队)、dequeue(出队)、front(查看队首)\n"
		explanation += "- 应用：任务调度、广度优先搜索、缓冲区\n"
		explanation += "- 时间复杂度：O(1) for all operations\n"
	} else if strings.Contains(query, "链表") || strings.Contains(query, "list") {
		explanation += "**链表 (Linked List)**\n"
		explanation += "- 动态数据结构，节点通过指针连接\n"
		explanation += "- 类型：单链表、双链表、循环链表\n"
		explanation += "- 优点：动态大小、插入删除高效\n"
		explanation += "- 缺点：随机访问慢、额外内存开销\n"
	} else {
		explanation += "**常见数据结构**\n"
		explanation += "- **数组**: 连续内存，随机访问O(1)\n"
		explanation += "- **链表**: 动态大小，插入删除O(1)\n"
		explanation += "- **栈**: LIFO，函数调用管理\n"
		explanation += "- **队列**: FIFO，任务调度\n"
		explanation += "- **树**: 层次结构，搜索排序\n"
		explanation += "- **图**: 网络关系，路径算法\n"
		explanation += "- **哈希表**: 快速查找，平均O(1)\n"
	}

	return &ExecutionResult{
		Type:        algorithm.DataStructure,
		Success:     true,
		Result:      nil,
		Explanation: explanation,
	}
}

// executeNumberTheory 执行数论算法
func (ae *AlgorithmExecutor) executeNumberTheory(request *algorithm.AlgorithmRequest) *ExecutionResult {
	query := strings.ToLower(request.Query)

	explanation := "🔢 **数论算法**\n\n"

	if strings.Contains(query, "质数") || strings.Contains(query, "素数") || strings.Contains(query, "prime") {
		explanation += "**质数判断与生成**\n"
		explanation += "- 质数：只能被1和自身整除的大于1的自然数\n"
		explanation += "- 判断方法：试除法、埃拉托斯特尼筛法\n"
		explanation += "- 应用：密码学、哈希函数、随机数生成\n"
		explanation += "- 示例：2, 3, 5, 7, 11, 13, 17, 19, 23, 29...\n"
	} else if strings.Contains(query, "最大公约数") || strings.Contains(query, "gcd") {
		explanation += "**最大公约数 (GCD)**\n"
		explanation += "- 欧几里得算法：gcd(a,b) = gcd(b, a%b)\n"
		explanation += "- 时间复杂度：O(log min(a,b))\n"
		explanation += "- 应用：分数化简、密码学、数论证明\n"
		explanation += "- 扩展欧几里得算法可求解线性丢番图方程\n"
	} else {
		explanation += "**数论基础概念**\n"
		explanation += "- **整除性**: a|b 表示 a 整除 b\n"
		explanation += "- **最大公约数**: gcd(a,b)，欧几里得算法\n"
		explanation += "- **最小公倍数**: lcm(a,b) = a*b/gcd(a,b)\n"
		explanation += "- **质数**: 只有1和自身两个因数\n"
		explanation += "- **模运算**: a ≡ b (mod m)\n"
		explanation += "- **费马小定理**: 若p为质数，则a^(p-1) ≡ 1 (mod p)\n"
	}

	return &ExecutionResult{
		Type:        algorithm.NumberTheory,
		Success:     true,
		Result:      nil,
		Explanation: explanation,
	}
}

// executeStatistics 执行统计算法
func (ae *AlgorithmExecutor) executeStatistics(request *algorithm.AlgorithmRequest) *ExecutionResult {
	explanation := "📈 **统计学算法**\n\n"

	// 从参数中获取数据
	var data []int
	if arr, exists := request.Parameters["array"]; exists {
		if intArr, ok := arr.([]int); ok {
			data = intArr
		}
	}

	if len(data) == 0 {
		// 使用默认示例数据
		data = []int{85, 92, 78, 96, 88, 76, 89, 94, 82, 90}
	}

	// 计算统计量
	mean := ae.calculateMean(data)
	median := ae.calculateMedian(data)
	mode := ae.calculateMode(data)
	variance := ae.calculateVariance(data, mean)
	stdDev := ae.calculateStandardDeviation(variance)

	explanation += fmt.Sprintf("**数据集**: %v\n\n", data)
	explanation += fmt.Sprintf("**平均数 (Mean)**: %.2f\n", mean)
	explanation += fmt.Sprintf("**中位数 (Median)**: %.2f\n", median)
	explanation += fmt.Sprintf("**众数 (Mode)**: %v\n", mode)
	explanation += fmt.Sprintf("**方差 (Variance)**: %.2f\n", variance)
	explanation += fmt.Sprintf("**标准差 (Standard Deviation)**: %.2f\n\n", stdDev)

	explanation += "**统计量说明**:\n"
	explanation += "- **平均数**: 所有数据的算术平均值\n"
	explanation += "- **中位数**: 排序后位于中间的数值\n"
	explanation += "- **众数**: 出现频率最高的数值\n"
	explanation += "- **方差**: 数据分散程度的度量\n"
	explanation += "- **标准差**: 方差的平方根，与原数据同单位\n"

	return &ExecutionResult{
		Type:    algorithm.Statistics,
		Success: true,
		Result: map[string]float64{
			"mean":     mean,
			"median":   median,
			"variance": variance,
			"stddev":   stdDev,
		},
		Explanation: explanation,
	}
}

// 统计计算辅助函数
func (ae *AlgorithmExecutor) calculateMean(data []int) float64 {
	sum := 0
	for _, v := range data {
		sum += v
	}
	return float64(sum) / float64(len(data))
}

func (ae *AlgorithmExecutor) calculateMedian(data []int) float64 {
	sorted := make([]int, len(data))
	copy(sorted, data)

	// 简单排序
	for i := 0; i < len(sorted)-1; i++ {
		for j := 0; j < len(sorted)-i-1; j++ {
			if sorted[j] > sorted[j+1] {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}

	n := len(sorted)
	if n%2 == 0 {
		return float64(sorted[n/2-1]+sorted[n/2]) / 2.0
	}
	return float64(sorted[n/2])
}

func (ae *AlgorithmExecutor) calculateMode(data []int) []int {
	freq := make(map[int]int)
	for _, v := range data {
		freq[v]++
	}

	maxFreq := 0
	for _, f := range freq {
		if f > maxFreq {
			maxFreq = f
		}
	}

	var modes []int
	for v, f := range freq {
		if f == maxFreq {
			modes = append(modes, v)
		}
	}

	return modes
}

func (ae *AlgorithmExecutor) calculateVariance(data []int, mean float64) float64 {
	sum := 0.0
	for _, v := range data {
		diff := float64(v) - mean
		sum += diff * diff
	}
	return sum / float64(len(data))
}

func (ae *AlgorithmExecutor) calculateStandardDeviation(variance float64) float64 {
	return ae.sqrt(variance)
}

// 简单的平方根计算
func (ae *AlgorithmExecutor) sqrt(x float64) float64 {
	if x == 0 {
		return 0
	}

	guess := x / 2
	for i := 0; i < 10; i++ { // 牛顿法迭代
		guess = (guess + x/guess) / 2
	}
	return guess
}
