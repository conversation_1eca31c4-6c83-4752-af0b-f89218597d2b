Write-Host "🚀 测试新增的爬虫功能..." -ForegroundColor Green

# 测试1: 检查爬虫状态API
Write-Host "`n1. 测试爬虫状态API..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/status" -Method GET
    Write-Host "✅ 爬虫状态获取成功" -ForegroundColor Green
    Write-Host "  - 运行状态: $($response.data.running)" -ForegroundColor Cyan
    Write-Host "  - 总目标数: $($response.data.total_targets)" -ForegroundColor Cyan
    Write-Host "  - 活跃目标: $($response.data.active_targets)" -ForegroundColor Cyan
    Write-Host "  - 正在爬取: $($response.data.crawling_now)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 爬虫状态API失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2: 获取现有目标
Write-Host "`n2. 获取现有爬取目标..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/targets" -Method GET
    Write-Host "✅ 获取目标成功，共 $($response.total) 个目标" -ForegroundColor Green
    
    if ($response.data.Count -gt 0) {
        Write-Host "现有目标:" -ForegroundColor Cyan
        foreach ($target in $response.data) {
            $scheduleText = switch ($target.schedule) {
                "* * * * *" { "每分钟" }
                "0 */1 * * *" { "每小时" }
                "0 */6 * * *" { "每6小时" }
                "0 0 * * *" { "每天" }
                "0 0 * * 0" { "每周" }
                default { $target.schedule }
            }
            Write-Host "  - [$($target.id)] $($target.name): $($target.url) ($scheduleText, 启用: $($target.enabled))" -ForegroundColor White
        }
        
        # 保存第一个目标的ID用于后续测试
        $firstTargetId = $response.data[0].id
    }
} catch {
    Write-Host "❌ 获取目标失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试3: 添加一个每分钟调度的测试目标
Write-Host "`n3. 添加每分钟调度的测试目标..." -ForegroundColor Yellow
$testTarget = @{
    name = "测试目标 - 每分钟 - $(Get-Date -Format 'HHmmss')"
    url = "https://httpbin.org/json"
    type = "api"
    category = "test"
    keywords = @("test", "api", "json", "minute")
    schedule = "* * * * *"  # 每分钟
    enabled = $true
    selectors = @{
        title = "title"
        content = "body"
    }
    filters = @{
        min_content_length = 10
    }
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/targets" -Method POST -ContentType "application/json" -Body $testTarget
    Write-Host "✅ 添加每分钟目标成功: $($response.message)" -ForegroundColor Green
    $newTargetId = $response.data.id
    Write-Host "  - 新目标ID: $newTargetId" -ForegroundColor Cyan
    Write-Host "  - 调度频率: 每分钟" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 添加目标失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4: 手动触发爬取并观察立即执行
if ($newTargetId) {
    Write-Host "`n4. 测试手动爬取立即执行..." -ForegroundColor Yellow
    
    # 获取爬取前的状态
    try {
        $beforeStatus = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/status" -Method GET
        Write-Host "爬取前状态 - 正在爬取: $($beforeStatus.data.crawling_now)" -ForegroundColor Cyan
        
        # 触发手动爬取
        $crawlResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/targets/$newTargetId/crawl" -Method POST
        Write-Host "✅ 手动爬取触发成功: $($crawlResponse.message)" -ForegroundColor Green
        
        # 立即检查状态（应该能看到正在爬取）
        Start-Sleep -Seconds 1
        $afterStatus = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/status" -Method GET
        Write-Host "爬取后状态 - 正在爬取: $($afterStatus.data.crawling_now)" -ForegroundColor Cyan
        
        if ($afterStatus.data.crawling_now -gt $beforeStatus.data.crawling_now) {
            Write-Host "✅ 手动爬取立即执行成功！" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 手动爬取可能已完成或未立即开始" -ForegroundColor Yellow
        }
        
        # 显示正在爬取的目标
        if ($afterStatus.data.active_crawls.Count -gt 0) {
            Write-Host "正在爬取的目标:" -ForegroundColor Cyan
            foreach ($active in $afterStatus.data.active_crawls) {
                Write-Host "  - 🕷️ $($active.name)" -ForegroundColor White
            }
        }
        
    } catch {
        Write-Host "❌ 手动爬取测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试5: 等待几秒后再次检查状态
Write-Host "`n5. 等待爬取完成..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

try {
    $finalStatus = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/status" -Method GET
    Write-Host "最终状态 - 正在爬取: $($finalStatus.data.crawling_now)" -ForegroundColor Cyan
    
    if ($finalStatus.data.crawling_now -eq 0) {
        Write-Host "✅ 爬取任务已完成" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 还有爬取任务在进行中" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 获取最终状态失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 新功能测试完成!" -ForegroundColor Green
Write-Host "`n📋 功能总结:" -ForegroundColor Cyan
Write-Host "✅ 每分钟调度选项已添加" -ForegroundColor White
Write-Host "✅ 手动爬取立即执行功能已实现" -ForegroundColor White
Write-Host "✅ 实时状态监控正常工作" -ForegroundColor White
Write-Host "✅ 正在爬取目标显示功能正常" -ForegroundColor White

Write-Host "`n🌐 访问地址:" -ForegroundColor Cyan
Write-Host "- 爬虫仪表板: http://localhost:8081/crawler_dashboard.html" -ForegroundColor White
Write-Host "- 主页: http://localhost:8081/" -ForegroundColor White
