-- 爬虫相关数据表

-- 1. 爬取目标表
CREATE TABLE IF NOT EXISTS crawl_targets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '目标名称',
    url TEXT NOT NULL COMMENT '目标URL',
    type ENUM('website', 'api', 'rss', 'search_engine') DEFAULT 'website' COMMENT '目标类型',
    category VARCHAR(100) DEFAULT 'general' COMMENT '知识分类',
    keywords JSON COMMENT '关键词',
    selectors JSON COMMENT 'CSS选择器配置',
    filters JSON COMMENT '过滤条件',
    schedule VARCHAR(50) DEFAULT '0 */6 * * *' COMMENT '调度表达式',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    last_crawled TIMESTAMP NULL COMMENT '最后爬取时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_enabled (enabled),
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_last_crawled (last_crawled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取目标表';

-- 2. 爬取结果表
CREATE TABLE IF NOT EXISTS crawl_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    target_id INT NOT NULL COMMENT '目标ID',
    url TEXT NOT NULL COMMENT '爬取URL',
    title VARCHAR(500) COMMENT '标题',
    content LONGTEXT COMMENT '内容',
    summary TEXT COMMENT '摘要',
    keywords JSON COMMENT '关键词',
    category VARCHAR(100) COMMENT '分类',
    metadata JSON COMMENT '元数据',
    crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '爬取时间',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    status ENUM('pending', 'processed', 'failed') DEFAULT 'pending' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (target_id) REFERENCES crawl_targets(id) ON DELETE CASCADE,
    INDEX idx_target_id (target_id),
    INDEX idx_status (status),
    INDEX idx_crawled_at (crawled_at),
    INDEX idx_processed_at (processed_at),
    INDEX idx_category (category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取结果表';

-- 3. 爬取日志表
CREATE TABLE IF NOT EXISTS crawl_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    target_id INT NOT NULL COMMENT '目标ID',
    url TEXT COMMENT '爬取URL',
    status ENUM('success', 'failed', 'timeout', 'error') NOT NULL COMMENT '状态',
    message TEXT COMMENT '日志消息',
    error_details TEXT COMMENT '错误详情',
    duration_ms INT COMMENT '耗时(毫秒)',
    response_code INT COMMENT 'HTTP响应码',
    response_size INT COMMENT '响应大小(字节)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (target_id) REFERENCES crawl_targets(id) ON DELETE CASCADE,
    INDEX idx_target_id (target_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取日志表';

-- 4. 爬虫配置表
CREATE TABLE IF NOT EXISTS crawler_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('string', 'int', 'float', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬虫配置表';

-- 5. 爬取统计表
CREATE TABLE IF NOT EXISTS crawl_statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    target_id INT NOT NULL COMMENT '目标ID',
    date DATE NOT NULL COMMENT '统计日期',
    total_crawls INT DEFAULT 0 COMMENT '总爬取次数',
    successful_crawls INT DEFAULT 0 COMMENT '成功爬取次数',
    failed_crawls INT DEFAULT 0 COMMENT '失败爬取次数',
    total_pages INT DEFAULT 0 COMMENT '总页面数',
    total_knowledge INT DEFAULT 0 COMMENT '提取知识数',
    avg_response_time INT DEFAULT 0 COMMENT '平均响应时间(ms)',
    total_data_size BIGINT DEFAULT 0 COMMENT '总数据大小(字节)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (target_id) REFERENCES crawl_targets(id) ON DELETE CASCADE,
    UNIQUE KEY uk_target_date (target_id, date),
    INDEX idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取统计表';

-- 插入默认配置
INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
('max_concurrency', '5', 'int', '最大并发爬取数'),
('request_delay', '2000', 'int', '请求间隔(毫秒)'),
('timeout', '30000', 'int', '请求超时(毫秒)'),
('user_agent', 'FAQ-System-Crawler/1.0', 'string', 'User-Agent'),
('max_retries', '3', 'int', '最大重试次数'),
('enable_javascript', 'false', 'boolean', '是否启用JavaScript渲染'),
('max_content_length', '1048576', 'int', '最大内容长度(字节)'),
('enable_robots_txt', 'true', 'boolean', '是否遵守robots.txt'),
('crawl_depth', '3', 'int', '最大爬取深度'),
('enable_duplicate_filter', 'true', 'boolean', '是否启用重复过滤');

-- 插入示例爬取目标
INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
('Python官方文档', 'https://docs.python.org/3/', 'website', 'technology', 
 '["python", "documentation", "programming"]', 
 '{"title": "title", "content": ".body"}', 
 '0 0 * * *', true),

('GitHub Trending', 'https://github.com/trending', 'website', 'technology',
 '["github", "trending", "opensource"]',
 '{"title": "h1", "content": ".Box-row"}',
 '0 */6 * * *', true),

('Stack Overflow Python', 'https://stackoverflow.com/questions/tagged/python', 'website', 'technology',
 '["python", "stackoverflow", "qa"]',
 '{"title": ".question-hyperlink", "content": ".post-text"}',
 '0 */4 * * *', true),

('MDN Web Docs', 'https://developer.mozilla.org/en-US/docs/Web/JavaScript', 'website', 'technology',
 '["javascript", "mdn", "web", "documentation"]',
 '{"title": "h1", "content": ".main-page-content"}',
 '0 0 * * *', true);
