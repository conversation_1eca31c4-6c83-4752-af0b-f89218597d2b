package main

import (
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/embedding"
	"faq-system/internal/vectorstore"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 加载配置
	cfg := config.Load()

	fmt.Println("🔍 测试向量搜索功能...")

	// 初始化向量存储
	store, err := vectorstore.NewVectorStore(cfg.VectorStore.Path)
	if err != nil {
		log.Fatalf("Failed to initialize vector store: %v", err)
	}
	defer store.Close()

	// 初始化嵌入客户端
	embedClient := embedding.NewClient(cfg.LocalAI.BaseURL, cfg.LocalAI.EmbedModel)

	// 测试查询
	testQueries := []string{
		"什么是LocalAI？",
		"LocalAI是什么？",
		"LocalAI介绍",
		"本地AI",
		"Go语言优势",
		"MySQL存储",
	}

	for _, query := range testQueries {
		fmt.Printf("\n🔍 测试查询: %s\n", query)

		// 生成查询向量
		queryVec, err := embedClient.EmbedText(query)
		if err != nil {
			fmt.Printf("❌ 生成查询向量失败: %v\n", err)
			continue
		}

		// 搜索相似向量
		results, err := store.SearchTopK(queryVec, 3)
		if err != nil {
			fmt.Printf("❌ 向量搜索失败: %v\n", err)
			continue
		}

		if len(results) == 0 {
			fmt.Println("❌ 没有找到相似结果")
			continue
		}

		fmt.Printf("✅ 找到 %d 个相似结果:\n", len(results))
		for i, result := range results {
			fmt.Printf("  %d. ID=%d, Score=%.4f, Content=%s\n",
				i+1, result.ID, result.Score, result.Content)
		}

		// 检查最佳匹配
		if results[0].Score > 0.3 {
			fmt.Printf("🎯 高置信度匹配 (%.1f%%)!\n", results[0].Score*100)
		} else if results[0].Score > 0.1 {
			fmt.Printf("🤔 中等置信度匹配 (%.1f%%)\n", results[0].Score*100)
		} else {
			fmt.Printf("😕 低置信度匹配 (%.1f%%)\n", results[0].Score*100)
		}
	}

	fmt.Println("\n📊 向量搜索测试完成")
}
