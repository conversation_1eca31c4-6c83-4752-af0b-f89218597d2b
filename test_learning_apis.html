<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习系统API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 学习系统API完整测试</h1>
        <p>测试所有学习系统API功能：性能统计、最近活动、优化建议等</p>
    </div>

    <div class="grid">
        <div class="container">
            <h2>📊 性能统计</h2>
            <div class="test-section">
                <button onclick="testPerformanceStats()">测试性能统计API</button>
                <div id="performance-result" class="result" style="display:none;"></div>
            </div>
        </div>

        <div class="container">
            <h2>📝 最近活动</h2>
            <div class="test-section">
                <button onclick="testRecentActivity()">测试最近活动API</button>
                <div id="activity-result" class="result" style="display:none;"></div>
            </div>
        </div>

        <div class="container">
            <h2>💡 优化建议</h2>
            <div class="test-section">
                <button onclick="testRecommendations()">测试优化建议API</button>
                <div id="recommendations-result" class="result" style="display:none;"></div>
            </div>
        </div>

        <div class="container">
            <h2>📈 学习指标</h2>
            <div class="test-section">
                <button onclick="testMetrics()">测试学习指标API</button>
                <div id="metrics-result" class="result" style="display:none;"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔄 综合测试</h2>
        <div class="test-section">
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="simulateUserInteraction()">模拟用户交互</button>
            <div id="comprehensive-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        const baseURL = 'http://localhost:8081';

        async function testPerformanceStats() {
            const resultDiv = document.getElementById('performance-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试性能统计API...';
            
            try {
                const response = await fetch(`${baseURL}/api/learning/performance`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testRecentActivity() {
            const resultDiv = document.getElementById('activity-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试最近活动API...';
            
            try {
                const response = await fetch(`${baseURL}/api/learning/activity?limit=10`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testRecommendations() {
            const resultDiv = document.getElementById('recommendations-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试优化建议API...';
            
            try {
                const response = await fetch(`${baseURL}/api/learning/recommendations`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testMetrics() {
            const resultDiv = document.getElementById('metrics-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试学习指标API...';
            
            try {
                const response = await fetch(`${baseURL}/api/learning/metrics`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function runAllTests() {
            const resultDiv = document.getElementById('comprehensive-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '运行所有测试...\n';
            
            const tests = [
                { name: '性能统计', url: '/api/learning/performance' },
                { name: '最近活动', url: '/api/learning/activity' },
                { name: '优化建议', url: '/api/learning/recommendations' },
                { name: '学习指标', url: '/api/learning/metrics' },
                { name: '学习配置', url: '/api/learning/config' }
            ];

            let results = '=== 综合测试结果 ===\n\n';
            
            for (const test of tests) {
                try {
                    const response = await fetch(`${baseURL}${test.url}`);
                    const data = await response.json();
                    results += `✅ ${test.name}: 成功 (${response.status})\n`;
                    results += `   数据量: ${JSON.stringify(data).length} 字符\n\n`;
                } catch (error) {
                    results += `❌ ${test.name}: 失败 - ${error.message}\n\n`;
                }
            }
            
            resultDiv.className = 'result success';
            resultDiv.textContent = results;
        }

        async function simulateUserInteraction() {
            const resultDiv = document.getElementById('comprehensive-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '模拟用户交互...\n';
            
            try {
                // 1. 提交问题
                const askResponse = await fetch(`${baseURL}/api/v1/ask`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question: '什么是Go语言？' })
                });
                const askData = await askResponse.json();
                
                // 2. 提交反馈
                const feedbackResponse = await fetch(`${baseURL}/api/learning/feedback`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        query_id: 1,
                        response_id: 1,
                        feedback_type: 'helpful',
                        rating: 5,
                        feedback_text: '测试反馈'
                    })
                });
                
                // 3. 提交行为数据
                const behaviorResponse = await fetch(`${baseURL}/api/learning/behavior`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        query_id: 1,
                        behavior_type: 'click',
                        behavior_data: { element: 'answer', timestamp: Date.now() }
                    })
                });
                
                let results = '=== 用户交互模拟结果 ===\n\n';
                results += `1. 问答请求: ${askResponse.status} - ${askData.answer ? '成功' : '失败'}\n`;
                results += `2. 反馈提交: ${feedbackResponse.status}\n`;
                results += `3. 行为记录: ${behaviorResponse.status}\n\n`;
                results += `问答响应: ${askData.answer}\n`;
                
                resultDiv.className = 'result success';
                resultDiv.textContent = results;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `模拟交互失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
