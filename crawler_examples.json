{"crawler_targets": [{"name": "Python官方文档", "url": "https://docs.python.org/3/", "type": "website", "category": "technology", "keywords": ["python", "documentation", "programming", "官方文档"], "selectors": {"title": "title", "content": ".body, .document"}, "filters": {"min_content_length": 100, "exclude_patterns": ["404", "error", "not found"]}, "schedule": "0 0 * * *", "enabled": true}, {"name": "Go语言官方博客", "url": "https://blog.golang.org/", "type": "website", "category": "technology", "keywords": ["golang", "go", "programming", "blog"], "selectors": {"title": "h1, .title", "content": ".article, .post-content"}, "schedule": "0 */12 * * *", "enabled": true}, {"name": "GitHub Trending", "url": "https://github.com/trending", "type": "website", "category": "technology", "keywords": ["github", "trending", "opensource", "repository"], "selectors": {"title": "h1", "content": ".Box-row"}, "schedule": "0 */6 * * *", "enabled": true}, {"name": "Stack Overflow Python标签", "url": "https://stackoverflow.com/questions/tagged/python", "type": "website", "category": "technology", "keywords": ["python", "stackoverflow", "qa", "问答"], "selectors": {"title": ".question-hyperlink", "content": ".post-text, .answer"}, "filters": {"min_content_length": 50, "max_content_length": 5000}, "schedule": "0 */4 * * *", "enabled": true}, {"name": "MDN Web文档", "url": "https://developer.mozilla.org/en-US/docs/Web/JavaScript", "type": "website", "category": "technology", "keywords": ["javascript", "mdn", "web", "documentation"], "selectors": {"title": "h1", "content": ".main-page-content, article"}, "schedule": "0 0 * * *", "enabled": true}, {"name": "阮一峰的网络日志", "url": "http://www.ruanyifeng.com/blog/", "type": "website", "category": "technology", "keywords": ["技术", "编程", "前端", "javascript"], "selectors": {"title": "h1, .entry-title", "content": ".entry-content, .post"}, "schedule": "0 0 * * *", "enabled": true}, {"name": "Redis官方文档", "url": "https://redis.io/documentation", "type": "website", "category": "database", "keywords": ["redis", "database", "cache", "nosql"], "selectors": {"title": "h1", "content": ".content, .documentation"}, "schedule": "0 0 * * 0", "enabled": true}, {"name": "Docker官方文档", "url": "https://docs.docker.com/", "type": "website", "category": "devops", "keywords": ["docker", "container", "devops", "deployment"], "selectors": {"title": "h1", "content": ".content, .docs-content"}, "schedule": "0 0 * * 0", "enabled": true}, {"name": "Kubernetes官方博客", "url": "https://kubernetes.io/blog/", "type": "website", "category": "devops", "keywords": ["kubernetes", "k8s", "container", "orchestration"], "selectors": {"title": "h1, .post-title", "content": ".post-content, article"}, "schedule": "0 0 * * *", "enabled": true}, {"name": "机器学习资讯", "url": "https://machinelearningmastery.com/", "type": "website", "category": "ai", "keywords": ["machine learning", "ai", "deep learning", "python"], "selectors": {"title": "h1, .entry-title", "content": ".entry-content, .post-content"}, "filters": {"min_content_length": 200}, "schedule": "0 0 * * *", "enabled": false}], "crawler_config": {"max_concurrency": 5, "request_delay": 2000, "timeout": 30000, "user_agent": "FAQ-System-Crawler/1.0", "max_retries": 3, "enable_javascript": false, "max_content_length": 1048576, "enable_robots_txt": true, "crawl_depth": 1, "enable_duplicate_filter": true}, "schedule_examples": {"每分钟": "* * * * *", "每小时": "0 * * * *", "每6小时": "0 */6 * * *", "每天凌晨": "0 0 * * *", "每天中午": "0 12 * * *", "每周日": "0 0 * * 0", "每月1号": "0 0 1 * *", "工作日": "0 9 * * 1-5"}, "selector_examples": {"通用选择器": {"title": "h1, .title, .entry-title", "content": ".content, .post-content, .entry-content, article, main"}, "博客网站": {"title": ".post-title, .entry-title", "content": ".post-content, .entry-content"}, "文档网站": {"title": "h1, .page-title", "content": ".documentation, .docs-content, .content"}, "新闻网站": {"title": ".headline, .news-title", "content": ".article-body, .news-content"}, "论坛网站": {"title": ".topic-title, .thread-title", "content": ".post-body, .message-content"}}, "category_examples": ["technology", "programming", "database", "devops", "ai", "frontend", "backend", "mobile", "security", "cloud", "general"], "usage_instructions": {"添加目标": "将目标配置POST到 /api/crawler/targets", "启动爬虫": "POST请求到 /api/crawler/start", "查看结果": "GET请求到 /api/crawler/results", "管理界面": "访问 /crawler_dashboard.html"}}