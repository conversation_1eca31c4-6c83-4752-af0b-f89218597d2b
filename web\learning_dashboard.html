<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQ系统 - 数据驱动学习仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-label {
            color: #666;
            font-weight: 500;
        }

        .metric-value {
            color: #333;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:active {
            transform: translateY(0);
        }

        .actions {
            text-align: center;
            margin-bottom: 30px;
        }

        .recommendations {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .recommendation-item {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 8px 8px 0;
        }

        .recommendation-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .recommendation-desc {
            color: #666;
            font-size: 0.9rem;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-running {
            background: #28a745;
        }

        .status-stopped {
            background: #dc3545;
        }

        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 数据驱动学习仪表板</h1>
            <p>实时监控FAQ系统的学习状态和性能指标</p>
        </div>

        <div class="actions">
            <button class="btn" onclick="refreshData()">🔄 刷新数据</button>
            <button class="btn" onclick="triggerAnalysis()">🔍 触发学习分析</button>
            <button class="btn" onclick="exportData()">📊 导出数据</button>
        </div>

        <div class="dashboard" id="dashboard">
            <div class="card">
                <h3>📈 学习指标</h3>
                <div id="metrics-content" class="loading">加载中...</div>
            </div>

            <div class="card">
                <h3>⚙️ 系统状态</h3>
                <div id="status-content" class="loading">加载中...</div>
            </div>

            <div class="card">
                <h3>🎯 性能统计</h3>
                <div id="performance-content" class="loading">加载中...</div>
            </div>

            <div class="card">
                <h3>📝 最近活动</h3>
                <div id="activity-content" class="loading">加载中...</div>
            </div>
        </div>

        <div class="recommendations">
            <h3>💡 优化建议</h3>
            <div id="recommendations-content" class="loading">加载中...</div>
        </div>
    </div>

    <script>
        // 全局变量
        const baseURL = 'http://localhost:8081';
        let refreshInterval;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            // 每30秒自动刷新
            refreshInterval = setInterval(refreshData, 30000);
        });

        // 刷新所有数据
        async function refreshData() {
            try {
                await Promise.all([
                    loadMetrics(),
                    loadStatus(),
                    loadPerformanceStats(),
                    loadRecentActivity(),
                    loadRecommendations()
                ]);
            } catch (error) {
                console.error('刷新数据失败:', error);
            }
        }

        // 加载学习指标
        async function loadMetrics() {
            try {
                const response = await fetch(`${baseURL}/api/learning/metrics`);
                const metrics = await response.json();
                
                const content = document.getElementById('metrics-content');
                content.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">总查询数</span>
                        <span class="metric-value">${metrics.total_queries || 0}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">总响应数</span>
                        <span class="metric-value">${metrics.total_responses || 0}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">用户反馈数</span>
                        <span class="metric-value">${metrics.total_feedback || 0}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">正面反馈率</span>
                        <span class="metric-value">${(metrics.positive_feedback_rate * 100).toFixed(1)}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${metrics.positive_feedback_rate * 100}%"></div>
                    </div>
                    <div class="metric">
                        <span class="metric-label">平均置信度</span>
                        <span class="metric-value">${(metrics.avg_confidence_score * 100).toFixed(1)}%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">学习模式数</span>
                        <span class="metric-value">${metrics.learning_patterns_count || 0}</span>
                    </div>
                `;
            } catch (error) {
                document.getElementById('metrics-content').innerHTML = 
                    '<div class="error">加载指标失败: ' + error.message + '</div>';
            }
        }

        // 加载系统状态
        async function loadStatus() {
            try {
                const content = document.getElementById('status-content');
                content.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">
                            <span class="status-indicator status-running"></span>学习系统
                        </span>
                        <span class="metric-value">运行中</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">最后更新</span>
                        <span class="metric-value">${new Date().toLocaleString()}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">数据库连接</span>
                        <span class="metric-value">正常</span>
                    </div>
                `;
            } catch (error) {
                document.getElementById('status-content').innerHTML =
                    '<div class="error">加载状态失败: ' + error.message + '</div>';
            }
        }

        // 加载优化建议
        async function loadRecommendations() {
            try {
                const response = await fetch(`${baseURL}/api/learning/recommendations`);
                const data = await response.json();
                
                const content = document.getElementById('recommendations-content');
                if (data.recommendations && data.recommendations.length > 0) {
                    content.innerHTML = data.recommendations.map(rec => `
                        <div class="recommendation-item">
                            <div class="recommendation-title">FAQ ${rec.faq_id}: ${rec.query_text}</div>
                            <div class="recommendation-desc">
                                当前得分: ${(rec.current_score * 100).toFixed(1)}% → 
                                建议得分: ${(rec.suggested_score * 100).toFixed(1)}%
                                <br>问题: ${rec.issues.join(', ')}
                                <br>建议: ${rec.suggestions.join(', ')}
                            </div>
                        </div>
                    `).join('');
                } else {
                    content.innerHTML = '<div class="loading">暂无优化建议</div>';
                }
            } catch (error) {
                document.getElementById('recommendations-content').innerHTML = 
                    '<div class="error">加载建议失败: ' + error.message + '</div>';
            }
        }

        // 加载性能统计
        async function loadPerformanceStats() {
            try {
                const response = await fetch(`${baseURL}/api/learning/performance`);
                const stats = await response.json();

                const content = document.getElementById('performance-content');
                content.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">平均响应时间</span>
                        <span class="metric-value">${stats.response_time.avg_ms.toFixed(1)}ms</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">最快响应</span>
                        <span class="metric-value">${stats.response_time.min_ms.toFixed(1)}ms</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">最慢响应</span>
                        <span class="metric-value">${stats.response_time.max_ms.toFixed(1)}ms</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">24小时查询量</span>
                        <span class="metric-value">${stats.hourly_queries.reduce((sum, h) => sum + h.count, 0)}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">主要意图</span>
                        <span class="metric-value">${stats.intent_distribution.length > 0 ? stats.intent_distribution[0].intent : 'N/A'}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">热门FAQ</span>
                        <span class="metric-value">${stats.faq_hit_rate.length > 0 ? 'FAQ #' + stats.faq_hit_rate[0].faq_id : 'N/A'}</span>
                    </div>
                `;
            } catch (error) {
                document.getElementById('performance-content').innerHTML =
                    '<div class="error">加载性能统计失败: ' + error.message + '</div>';
            }
        }

        // 加载最近活动
        async function loadRecentActivity() {
            try {
                const response = await fetch(`${baseURL}/api/learning/activity?limit=10`);
                const data = await response.json();

                const content = document.getElementById('activity-content');
                if (data.activities && data.activities.length > 0) {
                    content.innerHTML = data.activities.map(activity => {
                        const time = new Date(activity.timestamp).toLocaleString();
                        let icon = '📝';
                        switch(activity.type) {
                            case 'query': icon = '❓'; break;
                            case 'feedback': icon = '👍'; break;
                            case 'pattern': icon = '🧠'; break;
                        }
                        return `
                            <div class="metric">
                                <span class="metric-label">${icon} ${activity.content}</span>
                                <span class="metric-value">${time}</span>
                            </div>
                        `;
                    }).join('');
                } else {
                    content.innerHTML = '<div class="loading">暂无最近活动</div>';
                }
            } catch (error) {
                document.getElementById('activity-content').innerHTML =
                    '<div class="error">加载最近活动失败: ' + error.message + '</div>';
            }
        }

        // 触发学习分析
        async function triggerAnalysis() {
            try {
                const response = await fetch(`${baseURL}/api/learning/analyze`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    alert('✅ 学习分析已启动！');
                    // 延迟刷新数据
                    setTimeout(refreshData, 2000);
                } else {
                    alert('❌ 启动学习分析失败');
                }
            } catch (error) {
                alert('❌ 请求失败: ' + error.message);
            }
        }

        // 导出数据
        function exportData() {
            // 这里可以实现数据导出功能
            alert('📊 数据导出功能开发中...');
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
