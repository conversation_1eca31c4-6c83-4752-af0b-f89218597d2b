@echo off
chcp 65001 >nul
echo ========================================
echo 启动 FAQ 智能问答系统
echo ========================================
echo.

echo 🚀 系统特性：
echo   ✅ RAG + 意图识别算法
echo   ✅ 上下文对话支持
echo   ✅ 向量语义搜索
echo   ✅ 模块化架构设计
echo   ✅ 自动数据库初始化
echo.

echo 🏗️ 项目结构：
echo   📁 cmd/           - 主程序入口
echo   📁 internal/      - 内部模块
echo   📁 config/        - 配置管理
echo   📁 database/      - 数据库初始化
echo   📁 embedding/     - 向量嵌入
echo   📁 health/        - 健康检查
echo   📁 logger/        - 日志系统
echo   📁 mysql/         - MySQL操作
echo   📁 vectorstore/   - 向量存储
echo   📁 rag/           - RAG聊天系统
echo.

echo 🎯 功能特性：
echo   🧠 意图识别：greeting, identity_inquiry, technical_question, follow_up, thanks
echo   💬 上下文对话：多轮对话记忆，智能追问处理
echo   🔍 智能搜索：精确匹配 + 向量语义搜索
echo   📊 置信度评估：高/中/低置信度不同回答策略
echo.

echo 🌐 访问地址：
echo   主页：http://localhost:8082
echo   测试页：http://localhost:8082/test
echo   健康检查：http://localhost:8082/health
echo.

echo 💡 测试建议：
echo   意图识别测试：
echo   - 问"你好" → greeting意图
echo   - 问"你是谁" → identity_inquiry意图
echo   - 问"什么是LocalAI" → technical_question意图
echo   - 问"谢谢" → thanks意图
echo.
echo   上下文对话测试：
echo   - 先问"什么是LocalAI"
echo   - 再问"如何安装" → 结合上下文理解
echo   - 再问"还有其他功能吗" → 继续上下文对话
echo.

echo 🔧 正在启动服务...
cd cmd
go run main.go

pause
