package main

import (
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🔄 开始重新创建学习表...")

	// 删除现有的学习表（按依赖关系顺序）
	dropTables := []string{
		"DROP TABLE IF EXISTS user_behaviors",
		"DROP TABLE IF EXISTS user_feedback",
		"DROP TABLE IF EXISTS system_responses",
		"DROP TABLE IF EXISTS user_queries",
		"DROP TABLE IF EXISTS learning_patterns",
		"DROP TABLE IF EXISTS faq_performance",
		"DROP TABLE IF EXISTS learning_config",
	}

	fmt.Println("🗑️  删除现有学习表...")
	for _, dropSQL := range dropTables {
		if _, err := db.Exec(dropSQL); err != nil {
			log.Printf("Warning: Failed to drop table: %v", err)
		}
	}

	// 重新创建学习表（使用正确的字符集）
	createTables := []string{
		// 1. 用户查询表
		`CREATE TABLE user_queries (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
			user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
			query_text TEXT NOT NULL COMMENT '用户查询内容',
			query_intent VARCHAR(50) COMMENT '识别的意图',
			query_type VARCHAR(50) COMMENT '问题类型',
			query_embedding JSON COMMENT '查询向量（JSON格式）',
			context_data JSON COMMENT '上下文信息',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_user_id (user_id),
			INDEX idx_session_id (session_id),
			INDEX idx_created_at (created_at),
			INDEX idx_intent (query_intent)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户查询记录表'`,

		// 2. 系统响应表
		`CREATE TABLE system_responses (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			query_id BIGINT NOT NULL COMMENT '关联的查询ID',
			matched_faq_id INT COMMENT '匹配的FAQ ID',
			response_text TEXT NOT NULL COMMENT '系统回答内容',
			response_source VARCHAR(100) COMMENT '回答来源',
			confidence_score FLOAT COMMENT '置信度得分',
			match_type VARCHAR(50) COMMENT '匹配类型',
			processing_time_ms INT COMMENT '处理时间（毫秒）',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
			FOREIGN KEY (matched_faq_id) REFERENCES faq(id) ON DELETE SET NULL,
			INDEX idx_query_id (query_id),
			INDEX idx_faq_id (matched_faq_id),
			INDEX idx_confidence (confidence_score)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统响应记录表'`,

		// 3. 用户反馈表
		`CREATE TABLE user_feedback (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			query_id BIGINT NOT NULL COMMENT '关联的查询ID',
			response_id BIGINT NOT NULL COMMENT '关联的响应ID',
			feedback_type ENUM('helpful', 'not_helpful', 'partially_helpful') NOT NULL COMMENT '反馈类型',
			rating INT CHECK (rating >= 1 AND rating <= 5) COMMENT '评分(1-5)',
			feedback_text TEXT COMMENT '文字反馈',
			improvement_suggestion TEXT COMMENT '改进建议',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
			FOREIGN KEY (response_id) REFERENCES system_responses(id) ON DELETE CASCADE,
			INDEX idx_query_id (query_id),
			INDEX idx_response_id (response_id),
			INDEX idx_feedback_type (feedback_type),
			INDEX idx_rating (rating)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户反馈表'`,

		// 4. 用户行为表
		`CREATE TABLE user_behaviors (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			query_id BIGINT NOT NULL COMMENT '关联的查询ID',
			behavior_type ENUM('click', 'scroll', 'copy', 'follow_up', 'exit') NOT NULL COMMENT '行为类型',
			behavior_data JSON COMMENT '行为详细数据',
			timestamp_ms BIGINT COMMENT '行为时间戳（毫秒）',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
			INDEX idx_query_id (query_id),
			INDEX idx_behavior_type (behavior_type),
			INDEX idx_timestamp (timestamp_ms)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为追踪表'`,

		// 5. 学习模式表
		`CREATE TABLE learning_patterns (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			pattern_type ENUM('query_similarity', 'intent_mapping', 'response_optimization', 'user_preference') NOT NULL COMMENT '模式类型',
			pattern_name VARCHAR(200) NOT NULL COMMENT '模式名称',
			pattern_data JSON NOT NULL COMMENT '模式数据',
			confidence FLOAT DEFAULT 0.0 COMMENT '模式置信度',
			usage_count INT DEFAULT 0 COMMENT '使用次数',
			success_rate FLOAT DEFAULT 0.0 COMMENT '成功率',
			last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_pattern_type (pattern_type),
			INDEX idx_confidence (confidence),
			INDEX idx_success_rate (success_rate),
			UNIQUE KEY uk_pattern_name (pattern_name)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习模式表'`,

		// 6. FAQ性能表
		`CREATE TABLE faq_performance (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			faq_id INT NOT NULL COMMENT 'FAQ ID',
			query_count INT DEFAULT 0 COMMENT '被查询次数',
			match_count INT DEFAULT 0 COMMENT '被匹配次数',
			positive_feedback INT DEFAULT 0 COMMENT '正面反馈数',
			negative_feedback INT DEFAULT 0 COMMENT '负面反馈数',
			avg_confidence FLOAT DEFAULT 0.0 COMMENT '平均置信度',
			avg_rating FLOAT DEFAULT 0.0 COMMENT '平均评分',
			last_matched TIMESTAMP COMMENT '最后匹配时间',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			FOREIGN KEY (faq_id) REFERENCES faq(id) ON DELETE CASCADE,
			UNIQUE KEY uk_faq_id (faq_id),
			INDEX idx_match_count (match_count),
			INDEX idx_avg_rating (avg_rating)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FAQ性能统计表'`,

		// 7. 学习配置表
		`CREATE TABLE learning_config (
			id INT AUTO_INCREMENT PRIMARY KEY,
			config_key VARCHAR(100) NOT NULL COMMENT '配置键',
			config_value TEXT NOT NULL COMMENT '配置值',
			config_type ENUM('string', 'int', 'float', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
			description TEXT COMMENT '配置描述',
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			UNIQUE KEY uk_config_key (config_key)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统学习配置表'`,
	}

	fmt.Println("🔨 创建新的学习表...")
	for i, createSQL := range createTables {
		if _, err := db.Exec(createSQL); err != nil {
			log.Fatalf("Failed to create table %d: %v", i+1, err)
		}
		fmt.Printf("✅ 创建表 %d/7\n", i+1)
	}

	// 插入默认配置
	fmt.Println("📝 插入默认学习配置...")
	defaultConfigs := []string{
		"INSERT IGNORE INTO learning_config (config_key, config_value, config_type, description) VALUES ('learning_enabled', 'true', 'boolean', '是否启用学习功能')",
		"INSERT IGNORE INTO learning_config (config_key, config_value, config_type, description) VALUES ('min_confidence_threshold', '0.3', 'float', '最小置信度阈值')",
		"INSERT IGNORE INTO learning_config (config_key, config_value, config_type, description) VALUES ('feedback_weight', '0.7', 'float', '用户反馈权重')",
		"INSERT IGNORE INTO learning_config (config_key, config_value, config_type, description) VALUES ('pattern_update_interval', '3600', 'int', '模式更新间隔（秒）')",
		"INSERT IGNORE INTO learning_config (config_key, config_value, config_type, description) VALUES ('max_learning_patterns', '1000', 'int', '最大学习模式数量')",
		"INSERT IGNORE INTO learning_config (config_key, config_value, config_type, description) VALUES ('auto_optimization', 'true', 'boolean', '是否启用自动优化')",
		"INSERT IGNORE INTO learning_config (config_key, config_value, config_type, description) VALUES ('data_retention_days', '90', 'int', '数据保留天数')",
	}

	for _, configSQL := range defaultConfigs {
		if _, err := db.Exec(configSQL); err != nil {
			log.Printf("Warning: Failed to insert config: %v", err)
		}
	}

	// 为现有FAQ创建性能记录
	fmt.Println("📊 初始化FAQ性能记录...")
	_, err = db.Exec(`
		INSERT IGNORE INTO faq_performance (faq_id, query_count, match_count, positive_feedback, negative_feedback, avg_confidence, avg_rating)
		SELECT id, 0, 0, 0, 0, 0.0, 0.0 FROM faq
	`)
	if err != nil {
		log.Printf("Warning: Failed to initialize FAQ performance: %v", err)
	}

	fmt.Println("🎉 学习表重新创建完成！")
	fmt.Println("✅ 所有表都使用了正确的UTF8MB4字符集")
	fmt.Println("✅ 中文字符编码问题已修复")
}
