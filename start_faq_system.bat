@echo off
echo ========================================
echo 启动FAQ系统 (模块化版本)
echo ========================================
echo.

echo 检查可执行文件...
if not exist "faq-system.exe" (
    echo 错误: faq-system.exe 不存在，请先运行构建命令
    echo 构建命令: go build -o faq-system.exe ./cmd
    pause
    exit /b 1
)

echo 启动FAQ系统...
echo 服务将在 http://localhost:8081 启动
echo.
echo 按 Ctrl+C 停止服务
echo ========================================

faq-system.exe

pause
