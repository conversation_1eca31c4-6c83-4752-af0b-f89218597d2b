package rag

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"faq-system/internal/algorithm"
	"faq-system/internal/algorithms"
	"faq-system/internal/answer"
	"faq-system/internal/embedding"
	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"faq-system/internal/mysql"
	"faq-system/internal/semantic"
	"faq-system/internal/smartmatch"
	"faq-system/internal/vectorstore"
)

// ChatSystem RAG聊天系统
type ChatSystem struct {
	intentClassifier    *IntentClassifier
	contextManager      *ContextManager
	vectorStore         *vectorstore.VectorStore
	embedClient         *embedding.Client
	faqs                []mysql.FAQ
	smartMatcher        *smartmatch.SmartMatcher
	semanticMatcher     *semantic.SmartMatcher
	answerGenerator     *answer.Generator
	algorithmRecognizer *algorithm.Recognizer
	algorithmExecutor   *algorithms.AlgorithmExecutor
	learningEngine      *learning.LearningEngine
	evolutionMatcher    *learning.SmartMatcher     // 新增：智能进化匹配器
	knowledgeLearner    *learning.KnowledgeLearner // 新增：知识学习器
}

// IntentClassifier 意图分类器
type IntentClassifier struct {
	patterns map[string][]string
}

// ContextManager 上下文管理器
type ContextManager struct {
	conversations map[string]*Conversation
	maxHistory    int
}

// Conversation 对话上下文
type Conversation struct {
	UserID    string
	SessionID string
	Messages  []Message
	History   []string
	Intent    string
	LastTime  time.Time
}

// Message 消息
type Message struct {
	Role      string
	Content   string
	Timestamp time.Time
	Intent    string
	Sources   []string
}

// Response RAG响应
type Response struct {
	Answer     string
	Source     string
	Intent     string
	Confidence float32
	Duration   time.Duration
}

// NewChatSystem 创建RAG聊天系统
func NewChatSystem(vectorStore *vectorstore.VectorStore, embedClient *embedding.Client, faqs []mysql.FAQ) *ChatSystem {
	return &ChatSystem{
		intentClassifier:    NewIntentClassifier(),
		contextManager:      NewContextManager(),
		vectorStore:         vectorStore,
		embedClient:         embedClient,
		faqs:                faqs,
		smartMatcher:        smartmatch.NewSmartMatcher(),
		semanticMatcher:     semantic.NewSmartMatcher(faqs),
		answerGenerator:     answer.NewAnswerGenerator(faqs),
		algorithmRecognizer: algorithm.NewRecognizer(),
		algorithmExecutor:   algorithms.NewAlgorithmExecutor(),
		learningEngine:      nil, // 将在SetLearningEngine中设置
	}
}

// SetLearningEngine 设置学习引擎
func (cs *ChatSystem) SetLearningEngine(engine *learning.LearningEngine) {
	cs.learningEngine = engine
}

// SetEvolutionMatcher 设置智能进化匹配器
func (cs *ChatSystem) SetEvolutionMatcher(matcher *learning.SmartMatcher) {
	cs.evolutionMatcher = matcher
}

// SetKnowledgeLearner 设置知识学习器
func (cs *ChatSystem) SetKnowledgeLearner(learner *learning.KnowledgeLearner) {
	cs.knowledgeLearner = learner
}

// getPreviousQuery 获取上一个查询
func (cs *ChatSystem) getPreviousQuery(context *Conversation) string {
	if len(context.History) > 0 {
		return context.History[len(context.History)-1]
	}
	return ""
}

// ProcessKnowledgeInput 处理用户提供的知识输入
func (cs *ChatSystem) ProcessKnowledgeInput(userID, query, userResponse string) (*Response, error) {
	if cs.knowledgeLearner == nil {
		return &Response{
			Answer:     "抱歉，知识学习功能暂时不可用。",
			Source:     "系统",
			Intent:     "system_message",
			Confidence: 0.0,
		}, nil
	}

	// 尝试从用户输入中学习知识
	err := cs.knowledgeLearner.LearnFromUserInput(userID, query, userResponse, "")
	if err != nil {
		logger.Errorf("知识学习失败: %v", err)
		return &Response{
			Answer:     "抱歉，我在学习您提供的知识时遇到了问题。",
			Source:     "系统",
			Intent:     "system_message",
			Confidence: 0.0,
		}, nil
	}

	// 检查是否成功学习到了知识
	pendingKnowledge, err := cs.knowledgeLearner.GetPendingKnowledge(1)
	if err == nil && len(pendingKnowledge) > 0 {
		// 自动批准高置信度的知识
		latestKnowledge := pendingKnowledge[0]
		if latestKnowledge.Confidence > 0.8 {
			cs.knowledgeLearner.ApproveKnowledge(latestKnowledge.ID)
			return &Response{
				Answer: fmt.Sprintf("✅ 谢谢您的教学！我已经学会了：\n\n**问题**: %s\n**答案**: %s\n\n这个知识已经被添加到我的知识库中，我可以用它来回答类似的问题。",
					latestKnowledge.Question, latestKnowledge.Answer),
				Source:     "知识学习系统",
				Intent:     "knowledge_learned",
				Confidence: 1.0,
			}, nil
		} else {
			return &Response{
				Answer: fmt.Sprintf("🧠 谢谢您的输入！我学到了一些新知识：\n\n**问题**: %s\n**答案**: %s\n\n这个知识正在审核中，审核通过后我就可以使用它来回答问题了。",
					latestKnowledge.Question, latestKnowledge.Answer),
				Source:     "知识学习系统",
				Intent:     "knowledge_pending",
				Confidence: 0.8,
			}, nil
		}
	}

	return &Response{
		Answer:     "🤔 我尝试从您的输入中学习，但没有识别出明确的知识模式。您可以尝试用\"X是Y\"的格式来教我新知识。",
		Source:     "知识学习系统",
		Intent:     "learning_guidance",
		Confidence: 0.5,
	}, nil
}

// NewIntentClassifier 创建意图分类器
func NewIntentClassifier() *IntentClassifier {
	patterns := map[string][]string{
		"greeting": {
			"你好", "hello", "hi", "嗨", "您好", "早上好", "下午好", "晚上好",
			"大家好", "各位好", "小伙伴们好", "朋友们好",
		},
		"identity_inquiry": {
			"你是谁", "你是什么", "你叫什么", "介绍一下自己", "你的身份", "你是",
		},
		"technical_question": {
			"什么是", "如何", "怎么", "为什么", "原理", "实现", "配置", "安装", "部署",
			"localai", "mysql", "数据库", "向量", "embedding", "go语言", "golang",
			"搜索", "系统", "架构", "开发", "编程", "技术", "代码", "框架", "工具",
		},
		"algorithm_request": {
			// 数学计算相关
			"计算", "算", "求", "等于", "多少", "结果", "答案", "数学", "运算", "求解", "解方程",
			"加", "减", "乘", "除", "加法", "减法", "乘法", "除法",
			"平方", "开方", "平方根", "立方", "次方", "幂", "指数", "对数", "三角函数",
			"sin", "cos", "tan", "log", "ln", "sqrt", "abs", "max", "min",
			"绝对值", "最大值", "最小值", "表达式", "公式", "方程",

			// 算法相关
			"算法", "排序", "搜索", "查找", "冒泡排序", "快速排序", "归并排序",
			"二分查找", "线性搜索", "动态规划", "图算法",

			// 统计相关
			"统计", "平均数", "中位数", "众数", "方差", "标准差", "概率",

			// 数论相关
			"质数", "素数", "最大公约数", "最小公倍数", "斐波那契",
		},
		"follow_up": {
			"继续", "详细说明", "举例", "更多信息", "具体怎么", "还有吗", "然后呢",
		},
		"thanks": {
			"谢谢", "感谢", "thank you", "thanks", "多谢",
		},
	}

	return &IntentClassifier{patterns: patterns}
}

// NewContextManager 创建上下文管理器
func NewContextManager() *ContextManager {
	return &ContextManager{
		conversations: make(map[string]*Conversation),
		maxHistory:    10,
	}
}

// ProcessQuery 处理查询
func (cs *ChatSystem) ProcessQuery(userID, query string) (*Response, error) {
	start := time.Now()

	// 1. 意图识别
	intent := cs.intentClassifier.ClassifyIntent(query)

	// 2. 获取对话上下文
	context := cs.contextManager.GetContext(userID)

	// 3. 数据收集 - 记录查询
	var queryID int64
	var embedding []float32
	if cs.learningEngine != nil {
		// 生成查询向量（如果可能）
		if cs.embedClient != nil {
			if embeddingVec, err := cs.embedClient.EmbedText(query); err == nil {
				embedding = embeddingVec
			}
		}

		// 创建查询上下文
		queryContext := &learning.QueryContext{
			SessionID:       context.SessionID,
			UserID:          userID,
			PreviousQueries: context.History,
			Timestamp:       time.Now(),
		}

		// 记录查询
		if id, err := cs.learningEngine.GetCollector().RecordQuery(
			context.SessionID, userID, query, intent, "", embedding, queryContext); err == nil {
			queryID = id
		}
	}

	// 3. 根据意图选择处理策略
	var response *Response
	var err error

	switch intent {
	case "greeting":
		response = cs.handleGreeting(query, context)
	case "technical_question":
		response, err = cs.handleTechnicalQuestion(query, context)
	case "algorithm_request":
		response, err = cs.handleAlgorithmRequest(query, context)
	case "identity_inquiry":
		response = cs.handleIdentityInquiry(query, context)
	case "follow_up":
		response, err = cs.handleFollowUp(query, context)
	case "thanks":
		response = cs.handleThanks(query, context)
	default:
		response, err = cs.handleGeneral(query, context)
	}

	if err != nil {
		return nil, err
	}

	// 4. 数据收集 - 记录响应
	processingTime := int(time.Since(start).Milliseconds())
	if cs.learningEngine != nil && queryID > 0 {
		var faqID *int
		// 尝试从响应中提取FAQ ID（如果有的话）
		// 这里需要根据实际的Response结构来实现

		cs.learningEngine.GetCollector().RecordResponse(
			queryID, faqID, response.Answer, response.Source,
			response.Intent, &response.Confidence, processingTime)
	}

	// 5. 尝试从用户交互中学习知识
	if cs.knowledgeLearner != nil {
		// 如果系统没有找到好的答案，可能用户会提供知识
		if response.Confidence < 0.5 {
			// 异步学习，不影响响应速度
			go func() {
				if err := cs.knowledgeLearner.LearnFromUserInput(userID, query, "", response.Answer); err != nil {
					logger.Warnf("知识学习失败: %v", err)
				}
			}()
		}
	}

	// 6. 更新对话上下文
	cs.contextManager.UpdateContext(userID, query, response.Answer, intent)

	response.Duration = time.Since(start)
	return response, nil
}

// ClassifyIntent 分类意图
func (ic *IntentClassifier) ClassifyIntent(query string) string {
	query = strings.ToLower(strings.TrimSpace(query))

	// 优先检查是否包含数学表达式特征
	if ic.containsMathExpression(query) {
		return "algorithm_request"
	}

	// 对于简单查询（单个技术词汇或过短查询），更谨慎处理
	isSimpleQuery := ic.isSimpleQuery(query)

	// 计算每个意图的匹配分数
	scores := make(map[string]float32)

	for intent, patterns := range ic.patterns {
		score := float32(0)
		for _, pattern := range patterns {
			if strings.Contains(query, pattern) {
				// 完全匹配得分更高
				if query == pattern {
					score += 2.0
				} else {
					score += 1.0
				}
			}
		}

		// 对简单查询进行特殊处理
		if isSimpleQuery && intent == "technical_question" {
			// 简单查询需要更强的技术问题特征才能被识别为技术问题
			if !ic.hasStrongTechnicalIndicators(query) {
				score *= 0.1 // 大幅降低简单查询的技术问题得分
			}
		}

		scores[intent] = score
	}

	// 对算法请求给予额外权重
	if scores["algorithm_request"] > 0 {
		scores["algorithm_request"] *= 1.2 // 增加20%权重
	}

	// 找到最高分的意图
	maxScore := float32(0)
	bestIntent := "general"

	for intent, score := range scores {
		if score > maxScore {
			maxScore = score
			bestIntent = intent
		}
	}

	// 最后检查：如果是简单查询但被识别为技术问题，强制改为general
	if isSimpleQuery && bestIntent == "technical_question" && !ic.hasStrongTechnicalIndicators(query) {
		bestIntent = "general"
	}

	return bestIntent
}

// containsMathExpression 检查是否包含数学表达式特征
func (ic *IntentClassifier) containsMathExpression(query string) bool {
	query = strings.TrimSpace(query)

	// 检查是否是简单的数学表达式格式（如 1+1= 或 1+1=?）
	if ic.isSimpleMathFormat(query) {
		return true
	}

	// 检查是否包含数字和运算符的组合
	hasNumber := false
	hasOperator := false

	for _, char := range query {
		if char >= '0' && char <= '9' {
			hasNumber = true
		}
		if strings.ContainsRune("+-*/=×÷", char) {
			hasOperator = true
		}
	}

	// 如果同时包含数字和运算符，很可能是数学表达式
	if hasNumber && hasOperator {
		return true
	}

	// 检查是否包含数学函数
	mathFunctions := []string{"sin", "cos", "tan", "log", "ln", "sqrt", "abs", "exp", "pow"}
	for _, fn := range mathFunctions {
		if strings.Contains(query, fn) {
			return true
		}
	}

	// 检查是否包含明确的数学计算词汇
	mathWords := []string{"计算", "求", "等于", "多少", "结果"}
	for _, word := range mathWords {
		if strings.Contains(query, word) && hasNumber {
			return true
		}
	}

	return false
}

// isSimpleMathFormat 检查是否是简单的数学表达式格式
func (ic *IntentClassifier) isSimpleMathFormat(query string) bool {
	query = strings.ToLower(strings.TrimSpace(query))

	// 匹配 数字+运算符+数字+等号 的模式（如 1+1= 或 2*3=?）
	patterns := []string{
		`^\d+\s*[\+\-\*\/]\s*\d+\s*=\s*\??\s*$`,                    // 1+1= 或 1+1=?
		`^\d+\s*[\+\-\*\/]\s*\d+\s*[\+\-\*\/]\s*\d+\s*=\s*\??\s*$`, // 1+2*3= 或 1+2*3=?
	}

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, query); matched {
			return true
		}
	}

	return false
}

// isSimpleQuery 检查是否是简单查询（单个技术词汇或过短查询）
func (ic *IntentClassifier) isSimpleQuery(query string) bool {
	query = strings.TrimSpace(query)
	queryLower := strings.ToLower(query)

	// 过短的查询（少于3个字符）
	if len(strings.ReplaceAll(query, " ", "")) < 3 {
		return true
	}

	// 单个技术词汇列表
	singleTechWords := []string{
		"go", "golang", "mysql", "localai", "embedding", "vector", "rag",
		"java", "python", "javascript", "react", "vue", "node", "docker",
		"redis", "mongodb", "postgresql", "nginx", "apache", "linux",
		"windows", "macos", "git", "github", "gitlab", "kubernetes",
	}

	// 检查是否是单个技术词汇
	for _, word := range singleTechWords {
		if queryLower == word {
			return true
		}
	}

	// 检查是否是没有问题上下文的短技术词汇
	if len(query) <= 8 {
		hasQuestionWord := false
		questionWords := []string{
			"什么", "如何", "怎么", "为什么", "是什么", "怎样", "介绍", "说明",
			"what", "how", "why", "explain", "tell", "about",
		}

		for _, qword := range questionWords {
			if strings.Contains(queryLower, qword) {
				hasQuestionWord = true
				break
			}
		}

		if !hasQuestionWord {
			return true
		}
	}

	return false
}

// hasStrongTechnicalIndicators 检查是否有强技术问题指示词
func (ic *IntentClassifier) hasStrongTechnicalIndicators(query string) bool {
	// 强技术问题指示词 - 这些词汇明确表示用户在询问技术问题
	strongIndicators := []string{
		"什么是", "如何", "怎么", "为什么", "原理", "实现", "配置", "安装", "部署",
		"介绍", "解释", "说明", "教程", "文档", "使用方法", "操作步骤",
		"what", "how", "why", "explain", "tutorial", "documentation",
	}

	for _, indicator := range strongIndicators {
		if strings.Contains(query, indicator) {
			return true
		}
	}

	// 检查是否是常见的编程语言或技术词汇（新增）
	technicalTerms := []string{
		"c#", "python", "java", "javascript", "typescript", "go", "rust", "swift", "kotlin",
		"php", "ruby", "c++", "c", "scala", "dart", "r", "matlab", "perl", "shell",
		"mysql", "postgresql", "mongodb", "redis", "elasticsearch", "sqlite",
		"react", "vue", "angular", "node", "express", "spring", "django", "flask",
		"docker", "kubernetes", "git", "linux", "windows", "macos", "aws", "azure",
		"localai", "openai", "chatgpt", "ai", "ml", "深度学习", "机器学习", "人工智能",
		"api", "rest", "graphql", "json", "xml", "yaml", "html", "css", "sql",
		"算法", "数据结构", "设计模式", "架构", "微服务", "分布式", "云计算",
	}

	queryLower := strings.ToLower(query)
	for _, term := range technicalTerms {
		if queryLower == term || strings.Contains(queryLower, term) {
			return true
		}
	}

	// 检查是否是完整的技术问题句式
	questionPatterns := []string{
		".*语言.*", ".*系统.*", ".*数据库.*", ".*框架.*", ".*工具.*",
		".*开发.*", ".*编程.*", ".*代码.*", ".*算法.*", ".*架构.*",
	}

	for _, pattern := range questionPatterns {
		if matched, _ := regexp.MatchString(pattern, query); matched {
			return true
		}
	}

	return false
}

// GetContext 获取对话上下文
func (cm *ContextManager) GetContext(userID string) *Conversation {
	if conv, exists := cm.conversations[userID]; exists {
		return conv
	}

	// 创建新的对话上下文
	conv := &Conversation{
		UserID:    userID,
		SessionID: fmt.Sprintf("session_%s_%d", userID, time.Now().Unix()),
		Messages:  make([]Message, 0),
		History:   make([]string, 0),
		LastTime:  time.Now(),
	}
	cm.conversations[userID] = conv
	return conv
}

// UpdateContext 更新对话上下文
func (cm *ContextManager) UpdateContext(userID, query, answer, intent string) {
	conv := cm.GetContext(userID)

	// 添加用户消息
	conv.Messages = append(conv.Messages, Message{
		Role:      "user",
		Content:   query,
		Timestamp: time.Now(),
		Intent:    intent,
	})

	// 添加助手回复
	conv.Messages = append(conv.Messages, Message{
		Role:      "assistant",
		Content:   answer,
		Timestamp: time.Now(),
	})

	// 更新查询历史
	conv.History = append(conv.History, query)
	if len(conv.History) > cm.maxHistory {
		conv.History = conv.History[1:] // 移除最早的查询
	}

	// 保持历史记录在限制范围内
	if len(conv.Messages) > cm.maxHistory*2 {
		conv.Messages = conv.Messages[2:] // 移除最早的一轮对话
	}

	conv.LastTime = time.Now()
}

// handleGreeting 处理问候
func (cs *ChatSystem) handleGreeting(query string, context *Conversation) *Response {
	// 检查是否是回访用户
	isReturning := len(context.Messages) > 0

	var greeting string
	if isReturning {
		greeting = "😊 欢迎回来！很高兴再次为您服务！"
	} else {
		hour := time.Now().Hour()
		if hour < 12 {
			greeting = "🌅 早上好！欢迎使用智能FAQ系统！"
		} else if hour < 18 {
			greeting = "☀️ 下午好！欢迎使用智能FAQ系统！"
		} else {
			greeting = "🌙 晚上好！欢迎使用智能FAQ系统！"
		}
	}

	answer := greeting + "\n\n我是您的专业技术助手，可以为您解答：\n" +
		"• LocalAI和人工智能技术\n" +
		"• MySQL数据库和存储\n" +
		"• 向量搜索和语义匹配\n" +
		"• Go语言开发和部署\n" +
		"• 系统架构和优化\n\n" +
		"💡 您可以直接提问，我会根据上下文为您提供精准回答！"

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "greeting",
		Confidence: 1.0,
	}
}

// handleIdentityInquiry 处理身份询问
func (cs *ChatSystem) handleIdentityInquiry(query string, context *Conversation) *Response {
	// 根据上下文调整回答
	var intro string
	if len(context.Messages) > 0 {
		intro = "🤖 我是您一直在交流的智能FAQ技术助手！"
	} else {
		intro = "🤖 我是智能FAQ技术助手，很高兴认识您！"
	}

	answer := intro + "\n\n💡 我的能力包括：\n" +
		"• 理解您的问题意图\n" +
		"• 记住对话上下文\n" +
		"• 提供精准的技术解答\n" +
		"• 支持多轮深入交流\n" +
		"• 学习您的提问习惯\n\n" +
		"🎯 我专注于技术领域，让我们开始技术探讨吧！"

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "identity_inquiry",
		Confidence: 1.0,
	}
}

// handleThanks 处理感谢
func (cs *ChatSystem) handleThanks(query string, context *Conversation) *Response {
	responses := []string{
		"😊 不客气！很高兴能帮助到您！",
		"🤗 不用谢！这是我应该做的！",
		"😄 能帮到您我很开心！",
	}

	// 根据对话历史选择回应
	responseIndex := len(context.Messages) % len(responses)
	answer := responses[responseIndex] + "\n\n如果您还有其他技术问题，请随时提问。我会继续为您提供专业的技术支持！"

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "thanks",
		Confidence: 1.0,
	}
}

// handleTechnicalQuestion 处理技术问题
func (cs *ChatSystem) handleTechnicalQuestion(query string, context *Conversation) (*Response, error) {
	// 1. 首先搜索学习到的知识
	if cs.knowledgeLearner != nil {
		learnedKnowledge, err := cs.knowledgeLearner.SearchLearnedKnowledge(query, 3)
		if err != nil {
			logger.Warnf("搜索学习知识失败: %v", err)
		} else if len(learnedKnowledge) > 0 {
			bestKnowledge := learnedKnowledge[0]
			logger.Infof("🔍 找到学习知识: %s (置信度: %.2f)", bestKnowledge.Question, bestKnowledge.Confidence)

			// 降低置信度阈值，提高匹配率
			if bestKnowledge.Confidence > 0.5 {
				logger.Infof("🧠 使用学习知识回答: %s", bestKnowledge.Question)

				// 记录知识使用
				cs.knowledgeLearner.RecordKnowledgeUsage(bestKnowledge.ID, 0, context.UserID, bestKnowledge.Confidence, nil)

				return &Response{
					Answer:     bestKnowledge.Answer,
					Source:     fmt.Sprintf("学习知识库 (置信度: %.1f%%)", bestKnowledge.Confidence*100),
					Intent:     "technical_question",
					Confidence: bestKnowledge.Confidence,
				}, nil
			} else {
				logger.Infof("🤔 学习知识置信度不足: %.2f < 0.5", bestKnowledge.Confidence)
			}
		} else {
			logger.Infof("🔍 未找到匹配的学习知识")
		}
	}

	// 2. 尝试精确技术关键词匹配
	if exactAnswer := cs.tryExactTechnicalMatch(query); exactAnswer != "" {
		return &Response{
			Answer:     exactAnswer,
			Source:     "智能助手",
			Intent:     "technical_question",
			Confidence: 1.0,
		}, nil
	}

	// 3. 使用向量搜索
	return cs.handleVectorSearch(query, context)
}

// handleAlgorithmRequest 处理算法请求
func (cs *ChatSystem) handleAlgorithmRequest(query string, context *Conversation) (*Response, error) {
	// 使用算法识别器识别算法类型
	algorithmRequest := cs.algorithmRecognizer.RecognizeAlgorithm(query)

	// 降低置信度阈值，提高算法识别的敏感性
	if algorithmRequest.Type == algorithm.Unknown || algorithmRequest.Confidence < 0.1 {
		// 如果算法识别失败，尝试检查是否为简单数学表达式
		if cs.isSimpleMathQuery(query) {
			// 创建一个数学表达式请求
			algorithmRequest = &algorithm.AlgorithmRequest{
				Query:      query,
				Type:       algorithm.MathExpression,
				Expression: cs.extractMathFromQuery(query),
				Parameters: make(map[string]interface{}),
				Confidence: 0.8,
			}
		} else {
			// 回退到技术问题处理
			return cs.handleTechnicalQuestion(query, context)
		}
	}

	// 执行算法
	result := cs.algorithmExecutor.Execute(algorithmRequest)

	if !result.Success {
		// 执行失败，提供错误信息
		answer := fmt.Sprintf("🤔 算法执行遇到问题：%s\n\n", result.Error)
		answer += "💡 建议：\n"
		answer += "• 检查输入格式是否正确\n"
		answer += "• 确认算法参数是否完整\n"
		answer += "• 尝试重新描述您的问题\n\n"
		answer += "我可以帮您解答各种算法问题，包括：\n"
		answer += "• 数学计算：如 '计算 2+3*4'\n"
		answer += "• 排序算法：如 '冒泡排序 [3,1,4,1,5]'\n"
		answer += "• 搜索算法：如 '二分查找 23 在 [1,5,12,23,45]'\n"
		answer += "• 统计分析：如 '计算平均数 [85,92,78,96,88]'"

		return &Response{
			Answer:     answer,
			Source:     "算法引擎",
			Intent:     "algorithm_request",
			Confidence: 0.7,
		}, nil
	}

	// 成功执行，返回结果
	answer := result.Explanation

	// 添加算法类型信息
	algorithmTypeName := cs.algorithmRecognizer.GetAlgorithmTypeName(algorithmRequest.Type)
	answer += fmt.Sprintf("\n\n🔍 **算法类型**: %s\n", algorithmTypeName)
	answer += fmt.Sprintf("🎯 **识别置信度**: %.1f%%", algorithmRequest.Confidence*100)

	return &Response{
		Answer:     answer,
		Source:     "算法引擎",
		Intent:     "algorithm_request",
		Confidence: algorithmRequest.Confidence,
	}, nil
}

// handleFollowUp 处理追问
func (cs *ChatSystem) handleFollowUp(query string, context *Conversation) (*Response, error) {
	// 分析上一轮对话
	if len(context.Messages) < 2 {
		return cs.handleGeneral(query, context)
	}

	lastUserMsg := ""
	for i := len(context.Messages) - 1; i >= 0; i-- {
		if context.Messages[i].Role == "user" {
			lastUserMsg = context.Messages[i].Content
			break
		}
	}

	// 构建上下文化的追问回答
	contextualQuery := lastUserMsg + " " + query
	return cs.handleTechnicalQuestion(contextualQuery, context)
}

// handleGeneral 处理一般问题
func (cs *ChatSystem) handleGeneral(query string, context *Conversation) (*Response, error) {
	// 1. 首先搜索学习到的知识（最重要的改进！）
	if cs.knowledgeLearner != nil {
		learnedKnowledge, err := cs.knowledgeLearner.SearchLearnedKnowledge(query, 3)
		if err != nil {
			logger.Warnf("搜索学习知识失败: %v", err)
		} else if len(learnedKnowledge) > 0 {
			bestKnowledge := learnedKnowledge[0]
			logger.Infof("🔍 找到学习知识: %s (置信度: %.2f)", bestKnowledge.Question, bestKnowledge.Confidence)

			// 降低置信度阈值，提高匹配率
			if bestKnowledge.Confidence > 0.5 {
				logger.Infof("🧠 使用学习知识回答: %s", bestKnowledge.Question)

				// 记录知识使用
				cs.knowledgeLearner.RecordKnowledgeUsage(bestKnowledge.ID, 0, context.UserID, bestKnowledge.Confidence, nil)

				return &Response{
					Answer:     bestKnowledge.Answer,
					Source:     fmt.Sprintf("学习知识库 (置信度: %.1f%%)", bestKnowledge.Confidence*100),
					Intent:     "learned_knowledge",
					Confidence: bestKnowledge.Confidence,
				}, nil
			} else {
				logger.Infof("🤔 学习知识置信度不足: %.2f < 0.5", bestKnowledge.Confidence)
			}
		} else {
			logger.Infof("🔍 未找到匹配的学习知识")
		}
	}

	// 2. 检查是否是单个技术词汇，如果是，先尝试搜索FAQ
	if cs.isSingleTechnicalWord(query) {
		// 尝试向量搜索FAQ中的相关内容
		response, err := cs.handleVectorSearch(query, context)
		if err == nil && response.Confidence > 0.5 {
			// 如果找到了相关的FAQ答案，直接返回
			// 为单个技术词汇的回答添加特殊标识
			response.Answer = "📚 **相关技术资料**\n\n" + response.Answer
			return response, nil
		}

		// 如果没有找到相关答案，提供引导性回答
		return cs.handleLowQualityTechnicalQuery(query), nil
	}

	// 3. 对于其他一般问题，尝试向量搜索
	return cs.handleVectorSearch(query, context)
}

// tryExactTechnicalMatch 尝试精确技术匹配 - 只返回真正的精确匹配
func (cs *ChatSystem) tryExactTechnicalMatch(question string) string {
	// 这个方法应该只返回真正的精确匹配，不返回兜底回答
	// 对于没有精确匹配的情况，返回空字符串让系统走向量搜索流程

	// 检查是否是非常简单的问候或无意义输入
	q := strings.ToLower(strings.TrimSpace(question))

	// 只对极短或无意义的输入返回引导回答
	if len(q) <= 2 {
		switch q {
		case "你", "我", "他", "她", "它":
			return "🤔 请细说，希望能帮到您！我是技术FAQ助手，可以为您解答：\n\n• LocalAI和AI技术\n• MySQL数据库\n• 向量搜索\n• Go语言开发\n• 系统部署\n\n请详细描述您的问题～"
		case "好", "行", "嗯", "哦", "啊":
			return "😊 请细说您想了解的技术问题，我会尽力为您解答！"
		case "hi", "ok":
			return "👋 Hello! 请告诉我您想了解什么技术问题吧！"
		}
	}

	// 对于其他所有情况，返回空字符串，让系统走向量搜索流程
	return ""
}

// handleVectorSearch 处理向量搜索
func (cs *ChatSystem) handleVectorSearch(query string, context *Conversation) (*Response, error) {
	// 检查必要的组件是否可用
	if cs.embedClient == nil || cs.vectorStore == nil {
		// 如果向量搜索组件不可用，直接返回诚实的无匹配回答
		return cs.generateNoMatchResponse(query), nil
	}

	// 生成查询向量
	queryEmbedding, err := cs.embedClient.EmbedText(query)
	if err != nil {
		// 向量生成失败，返回诚实的无匹配回答
		return cs.generateNoMatchResponse(query), nil
	}

	// 向量搜索
	results, err := cs.vectorStore.SearchTopK(queryEmbedding, 5) // 增加候选数量
	if err != nil || len(results) == 0 {
		// 向量搜索失败或无结果，返回诚实的无匹配回答
		return cs.generateNoMatchResponse(query), nil
	}

	// 使用智能匹配器找到最佳匹配
	bestMatch := cs.semanticMatcher.FindBestMatch(query, results)
	if bestMatch == nil {
		return cs.generateFallbackResponse(query), nil
	}

	// 应用智能进化匹配（如果可用）
	if cs.evolutionMatcher != nil {
		// 创建匹配上下文
		userSegment, thinkingStyle := cs.evolutionMatcher.GetUserThinkingStyle(context.UserID)
		matchingContext := &learning.MatchingContext{
			Query:         query,
			UserID:        context.UserID,
			Intent:        "technical_question",
			UserSegment:   userSegment,
			ThinkingStyle: thinkingStyle,
			SessionLength: len(context.History),
			PreviousQuery: cs.getPreviousQuery(context),
			Metadata:      make(map[string]interface{}),
		}

		// 应用智能匹配优化
		matchingResult := cs.evolutionMatcher.ApplySmartMatching(matchingContext, bestMatch.Score)

		// 更新匹配分数和响应风格
		bestMatch.Score = matchingResult.AdjustedScore

		// 根据学习到的偏好调整响应
		if matchingResult.ResponseStyle == "concise" {
			// 用户偏好简洁回答
			bestMatch.Explanation = "" // 移除详细解释
		}

		logger.Infof("🧠 智能进化匹配: %.3f -> %.3f (规则: %v)",
			matchingResult.OriginalScore, matchingResult.AdjustedScore, matchingResult.AppliedRules)
	}

	// 根据匹配质量生成不同的回答
	var answer string
	var source string

	if bestMatch.Score > 0.8 {
		// 高质量匹配，直接返回答案
		answer = bestMatch.FAQ.Answer
		source = "FAQ数据库（精确匹配）"
	} else if bestMatch.Score > 0.6 {
		// 中等质量匹配，添加说明
		answer = fmt.Sprintf("🎯 **%s**\n\n%s\n\n💡 %s",
			bestMatch.MatchType, bestMatch.FAQ.Answer, bestMatch.Explanation)
		source = "FAQ数据库（语义匹配）"
	} else if bestMatch.Score > 0.4 {
		// 低质量匹配，提供相关信息但说明可能不完全匹配
		answer = fmt.Sprintf("🔍 我找到了一些可能相关的信息（相关度：%.1f%%）：\n\n%s\n\n⚠️ **注意**：这个答案可能不完全符合您的问题。如果您需要更具体的信息，建议您：\n• 提供更详细的问题描述\n• 尝试换个方式提问\n• 或者告诉我您想了解的具体方面",
			bestMatch.Score*100, bestMatch.FAQ.Answer)
		source = "FAQ数据库（低匹配度）"
	} else {
		// 匹配度太低，诚实告知没有找到合适答案
		return cs.generateNoMatchResponse(query), nil
	}

	return &Response{
		Answer:     answer,
		Source:     source,
		Intent:     "technical_question",
		Confidence: bestMatch.Score,
	}, nil
}

// getFAQByID 根据ID获取FAQ
func (cs *ChatSystem) getFAQByID(id int) *mysql.FAQ {
	for _, faq := range cs.faqs {
		if faq.ID == id {
			return &faq
		}
	}
	return nil
}

// formatConfidence 格式化置信度
func formatConfidence(confidence float32) string {
	return strings.TrimSuffix(strings.TrimSuffix(fmt.Sprintf("%.1f%%", confidence*100), "0"), ".") + "%"
}

// generateFallbackResponse 生成兜底回答 - 使用智能匹配器
func (cs *ChatSystem) generateFallbackResponse(query string) *Response {
	answer := cs.smartMatcher.GenerateFallbackAnswer(query)

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "general",
		Confidence: 0.5,
	}
}

// isSimpleMathQuery 检查是否为简单数学查询
func (cs *ChatSystem) isSimpleMathQuery(query string) bool {
	query = strings.ToLower(strings.TrimSpace(query))

	// 检查是否包含数字和运算符
	hasNumber := false
	hasOperator := false

	for _, char := range query {
		if char >= '0' && char <= '9' {
			hasNumber = true
		}
		if strings.ContainsRune("+-*/=×÷", char) {
			hasOperator = true
		}
	}

	// 检查是否包含计算相关词汇
	mathKeywords := []string{"计算", "求", "等于", "多少", "加", "减", "乘", "除"}
	hasMathKeyword := false
	for _, keyword := range mathKeywords {
		if strings.Contains(query, keyword) {
			hasMathKeyword = true
			break
		}
	}

	return (hasNumber && hasOperator) || (hasNumber && hasMathKeyword)
}

// extractMathFromQuery 从查询中提取数学表达式
func (cs *ChatSystem) extractMathFromQuery(query string) string {
	// 首先尝试使用正则表达式直接提取数学表达式
	if mathExpr := cs.extractPureMathExpression(query); mathExpr != "" {
		return mathExpr
	}

	// 移除常见的中文描述词
	removeWords := []string{
		"帮我", "请", "帮", "我", "计算", "算", "求", "一下", "多少", "结果", "答案", "是", "的", "什么", "等于",
	}

	cleaned := query
	for _, word := range removeWords {
		cleaned = strings.ReplaceAll(cleaned, word, "")
	}

	// 处理冒号分隔符
	if strings.Contains(cleaned, "：") {
		parts := strings.Split(cleaned, "：")
		if len(parts) > 1 {
			cleaned = parts[len(parts)-1] // 取冒号后面的部分
		}
	}
	if strings.Contains(cleaned, ":") {
		parts := strings.Split(cleaned, ":")
		if len(parts) > 1 {
			cleaned = parts[len(parts)-1] // 取冒号后面的部分
		}
	}

	// 处理等号情况：如果表达式以 = 或 =? 结尾，提取等号前面的部分
	if strings.HasSuffix(cleaned, "=?") {
		cleaned = strings.TrimSuffix(cleaned, "=?")
	} else if strings.HasSuffix(cleaned, "=") {
		cleaned = strings.TrimSuffix(cleaned, "=")
	}

	// 处理问号
	cleaned = strings.ReplaceAll(cleaned, "?", "")

	// 替换中文运算符
	cleaned = strings.ReplaceAll(cleaned, "加", "+")
	cleaned = strings.ReplaceAll(cleaned, "减", "-")
	cleaned = strings.ReplaceAll(cleaned, "乘", "*")
	cleaned = strings.ReplaceAll(cleaned, "除", "/")
	cleaned = strings.ReplaceAll(cleaned, "×", "*")
	cleaned = strings.ReplaceAll(cleaned, "÷", "/")

	// 移除多余空格
	cleaned = strings.TrimSpace(cleaned)

	return cleaned
}

// extractPureMathExpression 使用正则表达式直接提取纯数学表达式
func (cs *ChatSystem) extractPureMathExpression(text string) string {
	// 匹配数学表达式模式：数字、运算符、括号、小数点、空格
	patterns := []string{
		`\d+(?:\.\d+)?\s*[\+\-\*\/]\s*\d+(?:\.\d+)?(?:\s*[\+\-\*\/]\s*\d+(?:\.\d+)?)*`, // 基本四则运算
		`\d+(?:\.\d+)?\s*[\+\-\*\/]\s*\d+(?:\.\d+)?`,                                   // 简单两数运算
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		if match := re.FindString(text); match != "" {
			return strings.TrimSpace(match)
		}
	}

	return ""
}

// isLowQualityTechnicalQuery 检查是否是低质量的技术查询
func (cs *ChatSystem) isLowQualityTechnicalQuery(query string) bool {
	query = strings.TrimSpace(query)

	// 过短的查询（少于3个字符）
	if len(strings.ReplaceAll(query, " ", "")) < 3 {
		return true
	}

	// 单个技术词汇，没有问题上下文
	singleTechWords := []string{
		"go", "golang", "mysql", "localai", "embedding", "vector", "rag",
		"java", "python", "javascript", "react", "vue", "node", "docker",
	}

	queryLower := strings.ToLower(query)
	for _, word := range singleTechWords {
		if queryLower == word {
			return true
		}
	}

	// 没有问题词汇的技术词汇
	hasQuestionWord := false
	questionWords := []string{
		"什么", "如何", "怎么", "为什么", "是什么", "怎样", "介绍", "说明",
		"what", "how", "why", "explain", "tell", "about",
	}

	for _, qword := range questionWords {
		if strings.Contains(queryLower, qword) {
			hasQuestionWord = true
			break
		}
	}

	// 如果查询很短且没有问题词汇，认为是低质量查询
	if len(query) < 6 && !hasQuestionWord {
		return true
	}

	return false
}

// handleLowQualityTechnicalQuery 处理低质量技术查询
func (cs *ChatSystem) handleLowQualityTechnicalQuery(query string) *Response {
	query = strings.ToLower(strings.TrimSpace(query))

	// 根据查询内容提供引导性回答
	var answer string

	switch query {
	case "go", "golang":
		answer = "🔍 您想了解Go语言的什么方面呢？\n\n" +
			"我可以为您介绍：\n" +
			"• Go语言基础语法和特性\n" +
			"• Go语言开发环境配置\n" +
			"• Go语言项目结构和最佳实践\n" +
			"• Go语言并发编程\n" +
			"• Go语言Web开发\n\n" +
			"💡 请具体描述您的问题，比如：\n" +
			"- \"Go语言有什么特点？\"\n" +
			"- \"如何安装Go开发环境？\"\n" +
			"- \"Go语言适合做什么项目？\""

	case "mysql":
		answer = "🗄️ 您想了解MySQL的什么内容呢？\n\n" +
			"我可以为您介绍：\n" +
			"• MySQL数据库安装和配置\n" +
			"• SQL语法和查询优化\n" +
			"• 数据库设计和建模\n" +
			"• MySQL性能调优\n" +
			"• 数据备份和恢复\n\n" +
			"💡 请具体描述您的问题，比如：\n" +
			"- \"如何安装MySQL？\"\n" +
			"- \"MySQL索引如何优化？\"\n" +
			"- \"如何设计数据库表结构？\""

	case "localai":
		answer = "🤖 您想了解LocalAI的什么方面呢？\n\n" +
			"我可以为您介绍：\n" +
			"• LocalAI的安装和部署\n" +
			"• 模型配置和管理\n" +
			"• API接口使用方法\n" +
			"• 性能优化和故障排除\n\n" +
			"💡 请具体描述您的问题，比如：\n" +
			"- \"如何安装LocalAI？\"\n" +
			"- \"LocalAI支持哪些模型？\"\n" +
			"- \"如何配置LocalAI的API？\""

	default:
		answer = "🤔 您的问题似乎比较简短，我想为您提供更准确的帮助！\n\n" +
			"💡 建议您可以这样提问：\n" +
			"• 使用疑问词：\"什么是...\"、\"如何...\"、\"为什么...\"\n" +
			"• 描述具体场景：\"我想要实现...\"、\"遇到了...问题\"\n" +
			"• 提供上下文：\"在使用...时，出现了...\"\n\n" +
			"🎯 这样我就能为您提供更精准和有用的技术解答了！"
	}

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "technical_question",
		Confidence: 0.8,
	}
}

// isSingleTechnicalWord 检查是否是单个技术词汇
func (cs *ChatSystem) isSingleTechnicalWord(query string) bool {
	query = strings.ToLower(strings.TrimSpace(query))

	// 单个技术词汇列表
	singleTechWords := []string{
		"go", "golang", "mysql", "localai", "embedding", "vector", "rag",
		"java", "python", "javascript", "react", "vue", "node", "docker",
		"redis", "mongodb", "postgresql", "nginx", "apache", "linux",
		"windows", "macos", "git", "github", "gitlab", "kubernetes",
	}

	// 检查是否是单个技术词汇
	for _, word := range singleTechWords {
		if query == word {
			return true
		}
	}

	return false
}

// generateNoMatchResponse 生成没有匹配时的诚实回答
func (cs *ChatSystem) generateNoMatchResponse(query string) *Response {
	// 分析查询类型，提供针对性的建议
	var answer string

	// 检查是否是技术问题
	if cs.isTechnicalQuery(query) {
		answer = fmt.Sprintf("🤔 抱歉，我在FAQ数据库中没有找到关于「%s」的相关信息。\n\n💡 **建议您可以：**\n• 尝试用不同的关键词重新提问\n• 提供更多背景信息或具体场景\n• 将复杂问题拆分成几个简单问题\n\n🔍 **或者您可以问我：**\n• 系统相关：LocalAI、MySQL、向量搜索\n• Go语言相关：基础语法、特性优势\n• 部署相关：系统部署、环境配置\n\n我会尽力为您提供准确的帮助！", query)
	} else {
		answer = fmt.Sprintf("🤔 抱歉，我没有找到关于「%s」的相关信息。\n\n💡 **建议您可以：**\n• 换个方式描述您的问题\n• 提供更多具体信息\n• 或者问我其他相关问题\n\n我主要可以帮您解答技术相关的问题哦！", query)
	}

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "no_match",
		Confidence: 0.0,
	}
}

// isTechnicalQuery 检查是否是技术查询
func (cs *ChatSystem) isTechnicalQuery(query string) bool {
	techKeywords := []string{
		"go", "golang", "java", "python", "javascript", "node", "mysql", "redis",
		"docker", "kubernetes", "linux", "windows", "配置", "安装", "部署",
		"开发", "编程", "代码", "系统", "数据库", "服务器", "网络",
	}

	queryLower := strings.ToLower(query)
	for _, keyword := range techKeywords {
		if strings.Contains(queryLower, keyword) {
			return true
		}
	}

	return false
}
