package main

import (
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	fmt.Println("🔍 检查数据库中的学习知识...")

	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 检查learned_knowledge表是否存在
	var tableExists int
	err = db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = 'learned_knowledge'", cfg.MySQL.Database).Scan(&tableExists)
	if err != nil {
		log.Fatalf("检查表是否存在失败: %v", err)
	}

	if tableExists == 0 {
		fmt.Println("❌ learned_knowledge表不存在")
		return
	}

	fmt.Println("✅ learned_knowledge表存在")

	// 检查表中的数据
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&count)
	if err != nil {
		log.Fatalf("查询数据数量失败: %v", err)
	}

	fmt.Printf("📊 learned_knowledge表中有 %d 条记录\n", count)

	if count == 0 {
		fmt.Println("⚠️ 表中没有数据，尝试插入示例数据...")
		
		sampleKnowledge := []string{
			`INSERT INTO learned_knowledge 
			(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES 
			('什么是Python？', 'Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。', 'user_input', 0.9, 'technology', '["python", "编程语言", "高级语言"]', '用户教学输入', 'system', 'approved')`,

			`INSERT INTO learned_knowledge 
			(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES 
			('什么是Docker？', 'Docker是一个开源的容器化平台，可以将应用程序及其依赖项打包到轻量级、可移植的容器中。', 'user_input', 0.9, 'technology', '["docker", "容器", "平台"]', '用户教学输入', 'system', 'approved')`,
		}

		for i, sql := range sampleKnowledge {
			_, err := db.Exec(sql)
			if err != nil {
				fmt.Printf("❌ 插入示例数据 %d 失败: %v\n", i+1, err)
			} else {
				fmt.Printf("✅ 插入示例数据 %d 成功\n", i+1)
			}
		}
	}

	// 查询所有学习知识
	fmt.Println("\n📚 查询所有学习知识:")
	rows, err := db.Query("SELECT id, question, answer, source, confidence, status FROM learned_knowledge ORDER BY confidence DESC")
	if err != nil {
		log.Fatalf("查询学习知识失败: %v", err)
	}
	defer rows.Close()

	fmt.Println("ID\t问题\t\t\t答案\t\t\t来源\t置信度\t状态")
	fmt.Println("---\t---\t\t\t---\t\t\t---\t---\t---")

	for rows.Next() {
		var id int
		var question, answer, source, status string
		var confidence float64

		err := rows.Scan(&id, &question, &answer, &source, &confidence, &status)
		if err != nil {
			log.Printf("扫描行失败: %v", err)
			continue
		}

		// 截断长文本以便显示
		if len(question) > 20 {
			question = question[:20] + "..."
		}
		if len(answer) > 30 {
			answer = answer[:30] + "..."
		}

		fmt.Printf("%d\t%s\t%s\t%s\t%.2f\t%s\n", 
			id, question, answer, source, confidence, status)
	}

	// 测试搜索功能
	fmt.Println("\n🔍 测试搜索功能:")
	testQueries := []string{"Python", "什么是Python", "Docker", "容器"}

	for _, testQuery := range testQueries {
		fmt.Printf("\n查询: %s\n", testQuery)
		
		searchQuery := `
			SELECT id, question, answer, confidence 
			FROM learned_knowledge
			WHERE status IN ('approved', 'pending')
			AND (question LIKE CONCAT('%', ?, '%')
			     OR answer LIKE CONCAT('%', ?, '%'))
			ORDER BY confidence DESC
			LIMIT 3
		`
		
		searchRows, err := db.Query(searchQuery, testQuery, testQuery)
		if err != nil {
			fmt.Printf("  ❌ 搜索失败: %v\n", err)
			continue
		}
		
		found := false
		for searchRows.Next() {
			var id int
			var question, answer string
			var confidence float64
			
			err := searchRows.Scan(&id, &question, &answer, &confidence)
			if err != nil {
				continue
			}
			
			found = true
			fmt.Printf("  ✅ 找到: %s (置信度: %.2f)\n", question, confidence)
		}
		searchRows.Close()
		
		if !found {
			fmt.Printf("  ❌ 未找到匹配结果\n")
		}
	}

	fmt.Println("\n🎉 检查完成！")
}
