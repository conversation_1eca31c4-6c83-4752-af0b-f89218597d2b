# 🕷️ FAQ系统爬虫功能

## 概述

FAQ系统现已集成智能爬虫功能，可以持续地从网络上主动搜索和收集指定的知识内容，自动提取并保存到知识库中，为FAQ系统提供持续的知识更新。

## 🌟 主要功能

### 1. 多类型数据源支持
- **网站爬取** (website): 爬取HTML网页内容
- **API接口** (api): 调用REST API获取数据
- **RSS订阅** (rss): 订阅RSS/Atom源
- **搜索引擎** (search_engine): 从搜索引擎获取结果

### 2. 智能内容提取
- 自动提取网页标题和正文内容
- 生成内容摘要
- 提取技术关键词
- 支持自定义CSS选择器

### 3. 调度系统
- 支持cron表达式定时调度
- 可配置爬取频率（每小时、每天、每周等）
- 手动触发爬取功能

### 4. 知识学习集成
- 自动将爬取内容转换为问答知识
- 与现有学习系统无缝集成
- 支持知识置信度评估

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   调度器        │    │   爬虫引擎      │    │   知识处理器    │
│   Scheduler     │───▶│   Crawler       │───▶│   Processor     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   目标管理      │    │   内容提取      │    │   知识学习      │
│   Target Mgmt   │    │   Extraction    │    │   Learning      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 数据库表结构

### 1. crawl_targets (爬取目标表)
- 存储爬取目标的配置信息
- 包含URL、类型、调度规则等

### 2. crawl_results (爬取结果表)
- 存储爬取到的原始内容
- 记录爬取时间和处理状态

### 3. crawl_logs (爬取日志表)
- 记录爬取过程的详细日志
- 用于监控和故障排查

### 4. crawler_config (爬虫配置表)
- 存储爬虫系统的全局配置
- 如并发数、超时时间等

## 🚀 快速开始

### 1. 启动系统
```bash
# 启动FAQ系统（爬虫会自动启动）
go run cmd/main.go
```

### 2. 访问爬虫仪表板
打开浏览器访问：`http://localhost:8081/crawler_dashboard.html`

### 3. 管理爬取目标
- 查看现有目标
- 添加新的爬取目标
- 启用/禁用目标
- 手动触发爬取

## 🔧 配置说明

### 爬取目标配置示例
```json
{
  "name": "Python官方文档",
  "url": "https://docs.python.org/3/",
  "type": "website",
  "category": "technology",
  "keywords": ["python", "documentation", "programming"],
  "selectors": {
    "title": "title",
    "content": ".body"
  },
  "schedule": "0 0 * * *",
  "enabled": true
}
```

### 调度表达式说明
- `0 */1 * * *`: 每小时执行
- `0 */6 * * *`: 每6小时执行
- `0 0 * * *`: 每天执行
- `0 0 * * 0`: 每周执行

## 📡 API接口

### 爬虫管理
- `POST /api/crawler/start` - 启动爬虫
- `POST /api/crawler/stop` - 停止爬虫
- `GET /api/crawler/status` - 获取爬虫状态

### 目标管理
- `GET /api/crawler/targets` - 获取所有目标
- `POST /api/crawler/targets` - 添加新目标
- `GET /api/crawler/targets/:id` - 获取单个目标
- `PUT /api/crawler/targets/:id` - 更新目标
- `DELETE /api/crawler/targets/:id` - 删除目标
- `POST /api/crawler/targets/:id/crawl` - 手动爬取

### 结果查看
- `GET /api/crawler/results` - 获取爬取结果
- `GET /api/crawler/statistics` - 获取统计信息
- `GET /api/crawler/logs` - 获取爬取日志

## 🛠️ 开发指南

### 添加新的爬虫类型
1. 在 `crawler_methods.go` 中实现新的爬取方法
2. 在 `crawlTarget` 方法中添加类型判断
3. 更新数据库表的枚举类型

### 自定义内容提取
1. 修改 `extractKnowledgeFromContent` 方法
2. 添加特定领域的关键词提取逻辑
3. 实现更智能的问答生成

### 扩展调度功能
1. 集成cron表达式解析库
2. 实现更复杂的调度策略
3. 添加任务优先级管理

## 🔍 监控和调试

### 日志查看
爬虫系统会输出详细的日志信息：
```
🕷️ 启动知识爬虫...
📊 加载了 4 个爬取目标
🕷️ 开始爬取: Python官方文档 (https://docs.python.org/3/)
✅ 爬取完成: Python官方文档
```

### 性能监控
- 爬取成功率统计
- 平均响应时间
- 数据量统计
- 错误率分析

## 🚨 注意事项

### 1. 遵守robots.txt
爬虫系统会检查目标网站的robots.txt文件，请确保遵守网站的爬取规则。

### 2. 合理设置频率
避免过于频繁的爬取，建议：
- 大型网站：每天1-2次
- 中型网站：每6-12小时
- 小型网站：每1-4小时

### 3. 内容质量控制
- 设置最小内容长度阈值
- 过滤重复内容
- 验证提取的知识质量

## 🔮 未来规划

### 短期目标
- [ ] 完善API接口实现
- [ ] 添加更多内容提取算法
- [ ] 实现智能去重功能

### 中期目标
- [ ] 支持JavaScript渲染
- [ ] 添加图片和文件下载
- [ ] 实现分布式爬取

### 长期目标
- [ ] AI驱动的内容理解
- [ ] 自动发现新的数据源
- [ ] 知识图谱构建

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进爬虫系统！

### 开发环境设置
1. 确保Go 1.19+环境
2. 安装依赖：`go mod tidy`
3. 运行测试：`go test ./internal/crawler/...`

### 提交规范
- 功能：`feat: 添加新的爬虫类型支持`
- 修复：`fix: 修复爬取超时问题`
- 文档：`docs: 更新API文档`

---

📧 如有问题，请通过Issue联系我们！
