Write-Host "🕷️ 测试爬虫API功能..." -ForegroundColor Green

# 测试爬虫状态
Write-Host "`n1. 测试爬虫状态API..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/status" -Method GET
    Write-Host "✅ 爬虫状态: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Green
} catch {
    Write-Host "❌ 爬虫状态API失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试获取爬取目标
Write-Host "`n2. 测试获取爬取目标..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/targets" -Method GET
    Write-Host "✅ 爬取目标数量: $($response.total)" -ForegroundColor Green
    if ($response.data.Count -gt 0) {
        Write-Host "目标列表:" -ForegroundColor Cyan
        foreach ($target in $response.data) {
            Write-Host "  - $($target.name): $($target.url) (启用: $($target.enabled))" -ForegroundColor White
        }
    }
} catch {
    Write-Host "❌ 获取爬取目标失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试添加爬取目标
Write-Host "`n3. 测试添加爬取目标..." -ForegroundColor Yellow
$newTarget = @{
    name = "测试目标 - $(Get-Date -Format 'HHmmss')"
    url = "https://httpbin.org/json"
    type = "api"
    category = "test"
    keywords = @("test", "api", "json")
    schedule = "0 */6 * * *"
    enabled = $true
    selectors = @{
        title = "title"
        content = "body"
    }
    filters = @{
        min_content_length = 10
    }
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/targets" -Method POST -ContentType "application/json" -Body $newTarget
    Write-Host "✅ 添加目标成功: $($response.message)" -ForegroundColor Green
    $targetId = $response.data.id
    Write-Host "新目标ID: $targetId" -ForegroundColor Cyan
    
    # 测试手动爬取
    if ($targetId) {
        Write-Host "`n4. 测试手动爬取..." -ForegroundColor Yellow
        try {
            $crawlResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/targets/$targetId/crawl" -Method POST
            Write-Host "✅ 手动爬取启动: $($crawlResponse.message)" -ForegroundColor Green
        } catch {
            Write-Host "❌ 手动爬取失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # 等待一下再检查状态
        Start-Sleep -Seconds 2
        
        # 再次检查状态
        Write-Host "`n5. 检查爬取状态..." -ForegroundColor Yellow
        try {
            $statusResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/status" -Method GET
            Write-Host "✅ 当前正在爬取: $($statusResponse.data.crawling_now)" -ForegroundColor Green
            if ($statusResponse.data.active_crawls.Count -gt 0) {
                Write-Host "活跃爬取:" -ForegroundColor Cyan
                foreach ($active in $statusResponse.data.active_crawls) {
                    Write-Host "  - $($active.name)" -ForegroundColor White
                }
            }
        } catch {
            Write-Host "❌ 检查状态失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "❌ 添加目标失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试统计信息
Write-Host "`n6. 测试统计信息..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/crawler/statistics" -Method GET
    Write-Host "✅ 统计信息: $($response.data | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ 统计信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 爬虫API测试完成!" -ForegroundColor Green
Write-Host "`n📋 访问地址:" -ForegroundColor Cyan
Write-Host "- 爬虫仪表板: http://localhost:8081/crawler_dashboard.html" -ForegroundColor White
Write-Host "- 主页: http://localhost:8081/" -ForegroundColor White
Write-Host "- 学习仪表板: http://localhost:8081/learning_dashboard.html" -ForegroundColor White
