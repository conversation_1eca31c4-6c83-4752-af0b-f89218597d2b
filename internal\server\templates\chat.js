// 聊天界面JavaScript逻辑
let isWaiting = false;
let responseStats = {
    '智能助手': 0,
    'FAQ数据库': 0,
    'FAQ数据库（高匹配）': 0,
    'FAQ数据库（低匹配）': 0,
    '系统异常': 0,
    'total': 0
};

// 检查系统状态
async function checkSystemStatus() {
    try {
        const response = await fetch('/health');
        const data = await response.json();
        const statusEl = document.getElementById('systemStatus');

        if (data.healthy) {
            statusEl.textContent = '🟢 FAQ系统正常';
            statusEl.className = 'status healthy';
        } else {
            statusEl.textContent = '🟡 FAQ系统部分异常';
            statusEl.className = 'status healthy';
        }
    } catch (error) {
        const statusEl = document.getElementById('systemStatus');
        statusEl.textContent = '🔴 FAQ系统连接失败';
        statusEl.className = 'status unhealthy';
    }
}

// 添加消息到聊天界面
function addMessage(content, isUser = false, time = null) {
    const messagesContainer = document.getElementById('chatMessages');
    const welcomeMsg = messagesContainer.querySelector('.welcome-message');
    if (welcomeMsg) {
        welcomeMsg.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = 'message ' + (isUser ? 'user' : 'bot');

    const currentTime = time || new Date().toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
    });

    messageDiv.innerHTML = `
        <div class="message-avatar">${isUser ? '👤' : '🤖'}</div>
        <div class="message-content">
            ${content}
            <div class="message-time">${currentTime}</div>
        </div>
    `;

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// 显示输入指示器
function showTypingIndicator() {
    const messagesContainer = document.getElementById('chatMessages');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot';
    typingDiv.id = 'typingIndicator';
    typingDiv.innerHTML = `
        <div class="message-avatar">🤖</div>
        <div class="typing-indicator" style="display: block;">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
    `;
    messagesContainer.appendChild(typingDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// 隐藏输入指示器
function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// 发送问题
async function sendQuestion() {
    if (isWaiting) return;

    const input = document.getElementById('questionInput');
    const question = input.value.trim();

    if (!question) {
        alert('请输入问题');
        return;
    }

    // 添加用户消息
    addMessage(question, true);
    input.value = '';

    // 设置等待状态
    isWaiting = true;
    const sendBtn = document.getElementById('sendBtn');
    sendBtn.disabled = true;
    sendBtn.textContent = '发送中...';

    // 显示输入指示器
    showTypingIndicator();

    try {
        const response = await fetch('/ask', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ question: question })
        });

        const data = await response.json();

        // 隐藏输入指示器
        hideTypingIndicator();

        if (response.ok) {
            // 记录回答来源统计
            if (data.source) {
                responseStats[data.source] = (responseStats[data.source] || 0) + 1;
                responseStats.total += 1;
            }

            // 根据回答来源添加不同的标识
            let sourceIcon = '';
            let sourceText = '';
            if (data.source === '智能助手') {
                sourceIcon = '🤖';
                sourceText = '智能助手';
            } else if (data.source === 'FAQ数据库') {
                sourceIcon = '📚';
                sourceText = 'FAQ';
            } else if (data.source === 'FAQ数据库（高匹配）') {
                sourceIcon = '📋';
                sourceText = 'FAQ高匹配';
            } else if (data.source === 'FAQ数据库（低匹配）') {
                sourceIcon = '⚠️';
                sourceText = 'FAQ低匹配';
            } else if (data.source === '系统异常') {
                sourceIcon = '❌';
                sourceText = '无法解答';
            } else {
                sourceIcon = '💭';
                sourceText = '智能回复';
            }

            const answerWithInfo = data.answer +
                '<br><small style="opacity: 0.7;">' +
                sourceIcon + ' ' + sourceText + ' • ⏱️ ' + data.duration +
                '</small>';
            addMessage(answerWithInfo, false);
        } else {
            addMessage('❌ 抱歉，处理您的问题时出现错误：' + (data.error || '未知错误'), false);
        }
    } catch (error) {
        hideTypingIndicator();
        addMessage('❌ 网络连接错误，请检查服务是否正常运行', false);
    } finally {
        // 恢复发送状态
        isWaiting = false;
        sendBtn.disabled = false;
        sendBtn.textContent = '发送';
        input.focus();
    }
}

// 快速提问
function askQuestion(question) {
    document.getElementById('questionInput').value = question;
    sendQuestion();
}

// 处理回车键
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendQuestion();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    checkSystemStatus();
    document.getElementById('questionInput').focus();

    // 定期检查系统状态
    setInterval(checkSystemStatus, 30000);
});
