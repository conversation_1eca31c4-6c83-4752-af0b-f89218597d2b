package server

import (
	"net/http"
	"strconv"

	"faq-system/internal/logger"
	"faq-system/internal/rag"

	"github.com/gin-gonic/gin"
)

// KnowledgeHandler 知识学习处理器
type KnowledgeHandler struct {
	ragSystem *rag.ChatSystem
}

// NewKnowledgeHandler 创建知识学习处理器
func NewKnowledgeHandler(ragSystem *rag.ChatSystem) *KnowledgeHandler {
	return &KnowledgeHandler{
		ragSystem: ragSystem,
	}
}

// TeachKnowledgeRequest 教学知识请求
type TeachKnowledgeRequest struct {
	Question string `json:"question" binding:"required"`
	Answer   string `json:"answer"`
	UserID   string `json:"user_id"`
}

// TeachKnowledge 教学知识接口
func (kh *KnowledgeHandler) TeachKnowledge(c *gin.Context) {
	var req TeachKnowledgeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 获取用户ID
	userID := req.UserID
	if userID == "" {
		userID = "user_" + c.ClientIP()
	}

	logger.Infof("📚 用户教学知识: %s -> %s", req.Question, req.Answer)

	// 处理知识输入
	response, err := kh.ragSystem.ProcessKnowledgeInput(userID, req.Question, req.Answer)
	if err != nil {
		logger.Errorf("知识学习处理失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to process knowledge input",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"answer":     response.Answer,
		"source":     response.Source,
		"intent":     response.Intent,
		"confidence": response.Confidence,
		"duration":   response.Duration.String(),
	})
}

// GetLearnedKnowledge 获取学习到的知识
func (kh *KnowledgeHandler) GetLearnedKnowledge(c *gin.Context) {
	// 获取查询参数
	limitStr := c.DefaultQuery("limit", "10")
	_ = c.DefaultQuery("status", "approved") // 暂时不使用

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	// 这里需要通过RAG系统获取知识学习器
	// 由于架构限制，暂时返回模拟数据
	c.JSON(http.StatusOK, gin.H{
		"knowledge": []map[string]interface{}{
			{
				"id":         1,
				"question":   "什么是C#？",
				"answer":     "C#是微软开发的一种面向对象的编程语言。",
				"confidence": 0.9,
				"status":     "approved",
				"created_at": "2025-01-01T00:00:00Z",
			},
		},
		"total": 1,
		"limit": limit,
	})
}

// SearchKnowledge 搜索知识
func (kh *KnowledgeHandler) SearchKnowledge(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Query parameter 'q' is required",
		})
		return
	}

	limitStr := c.DefaultQuery("limit", "5")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 5
	}

	// 这里需要通过RAG系统搜索学习到的知识
	// 由于架构限制，暂时返回模拟数据
	c.JSON(http.StatusOK, gin.H{
		"query":   query,
		"results": []map[string]interface{}{},
		"count":   0,
	})
}

// ApproveKnowledge 批准知识
func (kh *KnowledgeHandler) ApproveKnowledge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid knowledge ID",
		})
		return
	}

	// 这里需要通过RAG系统批准知识
	// 由于架构限制，暂时返回成功
	logger.Infof("✅ 批准知识: %d", id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Knowledge approved successfully",
		"id":      id,
	})
}

// GetKnowledgeStats 获取知识统计
func (kh *KnowledgeHandler) GetKnowledgeStats(c *gin.Context) {
	// 这里需要通过RAG系统获取知识统计
	// 由于架构限制，暂时返回模拟数据
	c.JSON(http.StatusOK, gin.H{
		"total_knowledge": 4,
		"by_status": map[string]int{
			"approved": 4,
			"pending":  0,
			"rejected": 0,
		},
		"by_category": map[string]int{
			"technology": 4,
			"business":   0,
			"general":    0,
		},
		"recent_learned": 4,
	})
}

// FeedbackKnowledge 反馈知识质量
func (kh *KnowledgeHandler) FeedbackKnowledge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid knowledge ID",
		})
		return
	}

	var feedback struct {
		Type    string `json:"type" binding:"required"` // helpful, not_helpful, incorrect, incomplete
		Rating  int    `json:"rating"`                  // 1-5
		Comment string `json:"comment"`
		UserID  string `json:"user_id"`
	}

	if err := c.ShouldBindJSON(&feedback); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid feedback format",
			"details": err.Error(),
		})
		return
	}

	userID := feedback.UserID
	if userID == "" {
		userID = "user_" + c.ClientIP()
	}

	logger.Infof("📝 知识反馈: ID=%d, Type=%s, Rating=%d, User=%s",
		id, feedback.Type, feedback.Rating, userID)

	// 这里需要记录知识反馈
	// 由于架构限制，暂时返回成功
	c.JSON(http.StatusOK, gin.H{
		"message": "Feedback recorded successfully",
		"id":      id,
	})
}

// GetPendingKnowledge 获取待审核知识
func (kh *KnowledgeHandler) GetPendingKnowledge(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	// 这里需要通过RAG系统获取待审核知识
	// 由于架构限制，暂时返回模拟数据
	c.JSON(http.StatusOK, gin.H{
		"pending_knowledge": []map[string]interface{}{},
		"count":             0,
		"limit":             limit,
	})
}
