<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQ系统API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 FAQ系统API测试</h1>
        
        <div class="test-section">
            <h3>1. 健康检查测试</h3>
            <button onclick="testHealth()">测试 /api/v1/health</button>
            <div id="health-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 问答功能测试</h3>
            <input type="text" id="question-input" placeholder="输入您的问题..." value="什么是LocalAI？">
            <button onclick="testAsk()">测试 /api/v1/ask</button>
            <div id="ask-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 统计信息测试</h3>
            <button onclick="testStats()">测试 /api/v1/stats</button>
            <div id="stats-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 学习系统测试</h3>
            <button onclick="testLearningMetrics()">测试 /api/learning/metrics</button>
            <button onclick="testLearningFeedback()">测试提交反馈</button>
            <div id="learning-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        const baseURL = 'http://localhost:8081';

        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试中...';
            
            try {
                const response = await fetch(`${baseURL}/api/v1/health`);
                const data = await response.text();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态: ${response.status}\n响应: ${data}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testAsk() {
            const resultDiv = document.getElementById('ask-result');
            const question = document.getElementById('question-input').value;
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试中...';
            
            try {
                const response = await fetch(`${baseURL}/api/v1/ask`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ question: question })
                });
                
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testStats() {
            const resultDiv = document.getElementById('stats-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试中...';
            
            try {
                const response = await fetch(`${baseURL}/api/v1/stats`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testLearningMetrics() {
            const resultDiv = document.getElementById('learning-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试学习指标...';
            
            try {
                const response = await fetch(`${baseURL}/api/learning/metrics`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testLearningFeedback() {
            const resultDiv = document.getElementById('learning-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试提交反馈...';
            
            try {
                const response = await fetch(`${baseURL}/api/learning/feedback`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query_id: 1,
                        response_id: 1,
                        feedback_type: 'helpful',
                        rating: 5,
                        feedback_text: '测试反馈'
                    })
                });
                
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
