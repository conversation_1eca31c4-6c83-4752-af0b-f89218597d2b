package main

import (
	"database/sql"
	"fmt"
	"log"

	"faq-system/internal/config"
	"faq-system/internal/learning"
	"faq-system/internal/mysql"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := mysql.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🧠 测试智能进化系统...")

	// 创建学习管理器
	manager := learning.NewManager(db)

	// 获取进化引擎
	evolutionEngine := manager.GetEvolutionEngine()

	fmt.Println("\n1. 🔍 分析用户思维模式...")
	err = evolutionEngine.AnalyzeUserThinking()
	if err != nil {
		fmt.Printf("❌ 用户思维分析失败: %v\n", err)
	} else {
		fmt.Println("✅ 用户思维模式分析完成")
	}

	fmt.Println("\n2. 🚀 应用进化改进...")
	err = evolutionEngine.ApplyEvolutionaryImprovements()
	if err != nil {
		fmt.Printf("❌ 进化改进失败: %v\n", err)
	} else {
		fmt.Println("✅ 进化改进应用完成")
	}

	fmt.Println("\n3. 🎯 测试智能匹配器...")
	smartMatcher := learning.NewSmartMatcher(db)

	// 创建测试上下文
	context := &learning.MatchingContext{
		Query:         "什么是LocalAI？",
		UserID:        "test_user",
		Intent:        "technical_question",
		UserSegment:   "technical",
		ThinkingStyle: "direct",
		SessionLength: 1,
		PreviousQuery: "",
		Metadata:      make(map[string]interface{}),
	}

	// 应用智能匹配
	result := smartMatcher.ApplySmartMatching(context, 0.7)

	fmt.Printf("📊 匹配结果:\n")
	fmt.Printf("  原始分数: %.3f\n", result.OriginalScore)
	fmt.Printf("  调整分数: %.3f\n", result.AdjustedScore)
	fmt.Printf("  应用规则: %v\n", result.AppliedRules)
	fmt.Printf("  响应风格: %s\n", result.ResponseStyle)
	fmt.Printf("  详细程度: %s\n", result.DetailLevel)

	fmt.Println("\n4. 📈 查看系统进化历史...")
	err = showEvolutionHistory(db)
	if err != nil {
		fmt.Printf("❌ 查看进化历史失败: %v\n", err)
	}

	fmt.Println("\n5. 🧠 查看用户思维模式...")
	err = showThinkingPatterns(db)
	if err != nil {
		fmt.Printf("❌ 查看思维模式失败: %v\n", err)
	}

	fmt.Println("\n6. 📋 查看智能匹配规则...")
	err = showMatchingRules(db)
	if err != nil {
		fmt.Printf("❌ 查看匹配规则失败: %v\n", err)
	}

	fmt.Println("\n🎉 智能进化系统测试完成！")
	fmt.Println("\n💡 系统现在具备:")
	fmt.Println("  • 🧠 用户思维模式分析")
	fmt.Println("  • 🎯 智能匹配规则引擎")
	fmt.Println("  • 📈 系统进化历史追踪")
	fmt.Println("  • 🚀 自动优化和改进")
	fmt.Println("  • 🔄 持续学习和进化")
}

func showEvolutionHistory(db *sql.DB) error {
	query := `
		SELECT evolution_type, description, improvement_rate, applied_at
		FROM system_evolution 
		ORDER BY applied_at DESC 
		LIMIT 5
	`

	rows, err := db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	fmt.Println("📈 系统进化历史:")
	count := 0
	for rows.Next() {
		var evolutionType, description string
		var improvementRate float32
		var appliedAt string

		err := rows.Scan(&evolutionType, &description, &improvementRate, &appliedAt)
		if err != nil {
			continue
		}

		count++
		fmt.Printf("  %d. [%s] %s (改进率: %.2f%%) - %s\n",
			count, evolutionType, description, improvementRate*100, appliedAt)
	}

	if count == 0 {
		fmt.Println("  暂无进化历史记录")
	}

	return nil
}

func showThinkingPatterns(db *sql.DB) error {
	query := `
		SELECT pattern_type, pattern_name, user_segment, thinking_style, 
		       success_rate, usage_count
		FROM user_thinking_patterns 
		ORDER BY success_rate DESC, usage_count DESC 
		LIMIT 5
	`

	rows, err := db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	fmt.Println("🧠 用户思维模式:")
	count := 0
	for rows.Next() {
		var patternType, patternName, userSegment, thinkingStyle string
		var successRate float32
		var usageCount int

		err := rows.Scan(&patternType, &patternName, &userSegment, &thinkingStyle, &successRate, &usageCount)
		if err != nil {
			continue
		}

		count++
		fmt.Printf("  %d. [%s] %s - %s/%s (成功率: %.2f%%, 使用: %d次)\n",
			count, patternType, patternName, userSegment, thinkingStyle, successRate*100, usageCount)
	}

	if count == 0 {
		fmt.Println("  暂无思维模式记录")
	}

	return nil
}

func showMatchingRules(db *sql.DB) error {
	query := `
		SELECT rule_name, rule_type, effectiveness_score, usage_count, success_rate
		FROM smart_matching_rules 
		WHERE is_active = TRUE
		ORDER BY effectiveness_score DESC, usage_count DESC 
		LIMIT 5
	`

	rows, err := db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	fmt.Println("📋 智能匹配规则:")
	count := 0
	for rows.Next() {
		var ruleName, ruleType string
		var effectivenessScore, successRate float32
		var usageCount int

		err := rows.Scan(&ruleName, &ruleType, &effectivenessScore, &usageCount, &successRate)
		if err != nil {
			continue
		}

		count++
		fmt.Printf("  %d. [%s] %s (效果: %.2f, 使用: %d次, 成功率: %.2f%%)\n",
			count, ruleType, ruleName, effectivenessScore, usageCount, successRate*100)
	}

	if count == 0 {
		fmt.Println("  暂无匹配规则")
	}

	return nil
}
