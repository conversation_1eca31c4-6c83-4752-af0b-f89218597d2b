package semantic

import (
	"regexp"
	"strings"
)

// QuestionType 问题类型枚举
type QuestionType int

const (
	Unknown         QuestionType = iota
	WhatIs                       // 什么是...（定义类问题）
	HowTo                        // 如何...（操作类问题）
	WhyWhen                      // 为什么/什么时候...（原因类问题）
	Configuration                // 配置类问题
	Installation                 // 安装类问题
	Troubleshooting              // 故障排除类问题
	Comparison                   // 比较类问题
	BestPractice                 // 最佳实践类问题
)

// QuestionClassifier 问题分类器
type QuestionClassifier struct {
	patterns map[QuestionType][]string
	regexes  map[QuestionType][]*regexp.Regexp
}

// NewQuestionClassifier 创建问题分类器
func NewQuestionClassifier() *QuestionClassifier {
	qc := &QuestionClassifier{
		patterns: make(map[QuestionType][]string),
		regexes:  make(map[QuestionType][]*regexp.Regexp),
	}
	qc.initializePatterns()
	qc.compileRegexes()
	return qc
}

// initializePatterns 初始化问题模式
func (qc *QuestionClassifier) initializePatterns() {
	qc.patterns[WhatIs] = []string{
		"什么是", "什么叫", "定义", "介绍", "概念", "含义",
		"what is", "what are", "define", "definition", "introduce",
	}

	qc.patterns[HowTo] = []string{
		"如何", "怎么", "怎样", "如何使用", "使用方法", "操作步骤",
		"how to", "how do", "how can", "steps to", "way to",
	}

	qc.patterns[Configuration] = []string{
		"配置", "设置", "参数", "环境", "配置文件", "环境变量", "环境配置",
		"开发环境", "运行环境", "工作环境", "项目配置", "系统配置",
		"config", "configuration", "setup", "setting", "environment",
		"configure", "parameter", "env", "dev environment", "runtime",
	}

	qc.patterns[Installation] = []string{
		"安装", "部署", "搭建", "构建", "下载",
		"install", "installation", "deploy", "deployment", "build",
		"download", "setup",
	}

	qc.patterns[Troubleshooting] = []string{
		"错误", "问题", "故障", "异常", "报错", "失败", "不工作", "无法",
		"error", "issue", "problem", "trouble", "fail", "failure",
		"exception", "bug", "doesn't work", "not working",
	}

	qc.patterns[Comparison] = []string{
		"区别", "差异", "对比", "比较", "优缺点", "选择",
		"difference", "compare", "comparison", "vs", "versus",
		"pros and cons", "advantages", "disadvantages",
	}

	qc.patterns[BestPractice] = []string{
		"最佳实践", "建议", "推荐", "优化", "性能", "效率",
		"best practice", "recommendation", "optimize", "performance",
		"efficiency", "tips", "advice",
	}

	qc.patterns[WhyWhen] = []string{
		"为什么", "什么时候", "何时", "原因", "原理",
		"why", "when", "reason", "principle", "cause",
	}
}

// compileRegexes 编译正则表达式
func (qc *QuestionClassifier) compileRegexes() {
	// 配置类问题的特殊模式 - 增强技术词汇+配置的组合识别
	configPatterns := []string{
		`.*环境.*配置.*`,
		`.*开发.*环境.*`,
		`.*配置.*文件.*`,
		`.*设置.*参数.*`,
		`.*环境.*变量.*`,
		// 技术词汇+配置组合模式
		`.*(go|golang|java|python|node|mysql|redis).*环境.*配置.*`,
		`.*(go|golang|java|python|node|mysql|redis).*开发.*环境.*`,
		`.*(go|golang|java|python|node|mysql|redis).*配置.*`,
		`.*配置.*(go|golang|java|python|node|mysql|redis).*环境.*`,
		// 更精确的Go语言配置模式
		`.*go.*语言.*环境.*配置.*`,
		`.*go.*语言.*开发.*环境.*`,
		`.*golang.*环境.*配置.*`,
		`.*golang.*开发.*环境.*`,
	}

	// 安装类问题的特殊模式
	installPatterns := []string{
		`.*安装.*环境.*`,
		`.*搭建.*环境.*`,
		`.*部署.*系统.*`,
		`.*下载.*安装.*`,
		// 技术词汇+安装组合模式
		`.*(go|golang|java|python|node|mysql|redis).*安装.*`,
		`.*安装.*(go|golang|java|python|node|mysql|redis).*`,
	}

	for _, pattern := range configPatterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			qc.regexes[Configuration] = append(qc.regexes[Configuration], regex)
		}
	}

	for _, pattern := range installPatterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			qc.regexes[Installation] = append(qc.regexes[Installation], regex)
		}
	}
}

// ClassifyQuestion 分类问题类型
func (qc *QuestionClassifier) ClassifyQuestion(question string) QuestionType {
	question = strings.ToLower(strings.TrimSpace(question))

	// 首先检查正则表达式模式（优先级最高）
	for qType, regexes := range qc.regexes {
		for _, regex := range regexes {
			if regex.MatchString(question) {
				return qType
			}
		}
	}

	// 然后检查关键词模式
	scores := make(map[QuestionType]float32)

	for qType, patterns := range qc.patterns {
		score := float32(0)
		for _, pattern := range patterns {
			if strings.Contains(question, pattern) {
				// 完全匹配得分更高
				if question == pattern {
					score += 3.0
				} else if strings.HasPrefix(question, pattern) {
					score += 2.0
				} else {
					score += 1.0
				}
			}
		}
		scores[qType] = score
	}

	// 特殊加权：检查技术词汇+操作类型的组合
	qc.applyComboBonus(question, scores)

	// 找到最高分的问题类型
	maxScore := float32(0)
	bestType := Unknown

	for qType, score := range scores {
		if score > maxScore {
			maxScore = score
			bestType = qType
		}
	}

	return bestType
}

// applyComboBonus 应用组合关键词加成
func (qc *QuestionClassifier) applyComboBonus(question string, scores map[QuestionType]float32) {
	// 技术词汇列表
	techWords := []string{"go", "golang", "java", "python", "node", "mysql", "redis", "docker"}

	// 检查是否包含技术词汇
	hasTechWord := false
	for _, tech := range techWords {
		if strings.Contains(question, tech) {
			hasTechWord = true
			break
		}
	}

	if !hasTechWord {
		return
	}

	// 如果包含技术词汇，给相关操作类型额外加分
	if strings.Contains(question, "配置") || strings.Contains(question, "环境") {
		scores[Configuration] += 2.0 // 大幅加分
	}

	if strings.Contains(question, "安装") || strings.Contains(question, "部署") {
		scores[Installation] += 2.0
	}

	if strings.Contains(question, "如何") || strings.Contains(question, "怎么") {
		scores[HowTo] += 1.5
	}

	if strings.Contains(question, "什么是") || strings.Contains(question, "介绍") {
		scores[WhatIs] += 1.5
	}
}

// GetQuestionTypeName 获取问题类型名称
func (qc *QuestionClassifier) GetQuestionTypeName(qType QuestionType) string {
	switch qType {
	case WhatIs:
		return "定义类问题"
	case HowTo:
		return "操作类问题"
	case Configuration:
		return "配置类问题"
	case Installation:
		return "安装类问题"
	case Troubleshooting:
		return "故障排除类问题"
	case Comparison:
		return "比较类问题"
	case BestPractice:
		return "最佳实践类问题"
	case WhyWhen:
		return "原因类问题"
	default:
		return "未知类型"
	}
}

// IsConfigurationQuestion 检查是否是配置类问题
func (qc *QuestionClassifier) IsConfigurationQuestion(question string) bool {
	return qc.ClassifyQuestion(question) == Configuration
}

// IsInstallationQuestion 检查是否是安装类问题
func (qc *QuestionClassifier) IsInstallationQuestion(question string) bool {
	return qc.ClassifyQuestion(question) == Installation
}
